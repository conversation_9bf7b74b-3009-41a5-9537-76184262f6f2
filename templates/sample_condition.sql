-- Migration File Template
-- Description: Brief description of the changes this migration introduces.

-- 1. Check if the database exists, if not create it
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'sampledb') THEN
        CREATE DATABASE sampledb;
    END IF;
END $$;

-- Connect to the sampledb database
\c sampledb

-- 2. Check if the table exists, if not create it and populate it
DO $$
BEGIN
    IF NOT EXISTS (SELECT * FROM information_schema.tables WHERE table_name = 'employees') THEN
        CREATE TABLE employees (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100),
            position VARCHAR(100),
            salary INTEGER
        );

        INSERT INTO employees (name, position, salary)
        VALUES 
        ('<PERSON>', 'Software Engineer', 60000),
        ('<PERSON>', 'Product Manager', 70000),
        ('Alice', 'Designer', 55000);
    END IF;
END $$;

-- 3. Check if the department column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (SELECT * FROM information_schema.columns WHERE table_name = 'employees' AND column_name = 'department') THEN
        ALTER TABLE employees ADD COLUMN department VARCHAR(100);
    END IF;
END $$;

-- 4. Data migration commands
UPDATE employees SET department = 'Engineering' WHERE position = 'Software Engineer';

-- 5. Index or constraint changes
-- Check if the index exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (SELECT * FROM pg_indexes WHERE tablename = 'employees' AND indexname = 'idx_employees_department') THEN
        CREATE INDEX idx_employees_department ON employees(department);
    END IF;
END $$;
