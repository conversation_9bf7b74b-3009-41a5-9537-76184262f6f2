server_tokens off;
port_in_redirect off;

proxy_connect_timeout       600s;
proxy_send_timeout          600s;
proxy_read_timeout          600s;
send_timeout                600s;

#gzip on;
#gzip_proxied any;
#gzip_types text/plain text/xml text/css application/x-javascript;
#gzip_vary on;
#gzip_disable "MSIE [1-6]\.(?!.*SV1)";

# Expires map
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   max;
    application/javascript     max;
}

server {
	listen 80;
	server_name challenge.lif.id;
	return 301 https://$host$request_uri;
}

server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;

        server_name challenge.lif.id;

        location /LifPortal {
		proxy_ssl_server_name on;
                proxy_pass "http://************/LifPortal/";
        }

        location /ws {
                proxy_set_header   X-Forwarded-For $remote_addr;
        	proxy_set_header   Host $http_host;
        	proxy_set_header Upgrade websocket;
        	proxy_set_header Connection Upgrade;
                proxy_pass "https://127.0.0.1:8443$uri$is_args$args";
	}

        location /GetProfilePictureServlet {
                proxy_pass "http://*************:8080/lif$uri$is_args$args";
        }

        location / {
                proxy_pass "http://localhost:9008/";
        }

#                ssl_certificate /home/<USER>/ssl_2022/lif.pem;
#                ssl_certificate_key /home/<USER>/ssl_2022/lif.key;

               ssl_certificate /home/<USER>/ssl_2022/lif.id-cloudflare-public.pem;
               ssl_certificate_key /home/<USER>/ssl_2022/lif.id-cloudflare-private.key;
}


########################## TOMCAT ###################################

server {
        listen 80;
        server_name live.lif.id;
        return 301 https://$host$request_uri;
}

server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;

        server_name live.lif.id;
        access_log /var/log/nginx/tomcat-access.log;
        error_log /var/log/nginx/tomcat-error.log;

        location / {
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://127.0.0.1:8080/;
         }
	ssl_certificate /home/<USER>/ssl_2022/lif.id-cloudflare-public.pem;
        ssl_certificate_key /home/<USER>/ssl_2022/lif.id-cloudflare-private.key;
 }

#########################TOMCAT#####################################





############################################################



server {
        listen 80;
        server_name live.lif.fit;
        return 301 https://$host$request_uri;
}

server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
	#listen 80;

        server_name live.lif.fit;
        access_log /var/log/nginx/tomcat-access.lif.log;
        error_log /var/log/nginx/tomcat-error.lif.log;

        location / {
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://127.0.0.1:8080/;
         }
       # ssl_certificate /home/<USER>/ssl_2022/lif.id-cloudflare-public.pem;
       # ssl_certificate_key /home/<USER>/ssl_2022/lif.id-cloudflare-private.key;
	ssl_certificate /home/<USER>/lif.fit.pem;
	ssl_certificate_key /home/<USER>/lif.fit.key;
 }

#############################################################



server {
        listen 80;
        server_name challenge.lif.fit;
        return 301 https://$host$request_uri;
}

server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;

        server_name challenge.lif.fit;

        location /LifPortal {
                proxy_ssl_server_name on;
                proxy_pass "http://************/LifPortal/";
        }

        location /ws {
                proxy_set_header   X-Forwarded-For $remote_addr;
                proxy_set_header   Host $http_host;
                proxy_set_header Upgrade websocket;
                proxy_set_header Connection Upgrade;
                proxy_pass "https://127.0.0.1:8443$uri$is_args$args";
        }

        location /GetProfilePictureServlet {
                proxy_pass "http://*************:8080/lif$uri$is_args$args";
        }

        location / {
                proxy_pass "http://localhost:9008/";
        }

#                ssl_certificate /home/<USER>/ssl_2022/lif.pem;
#                ssl_certificate_key /home/<USER>/ssl_2022/lif.key;

               #ssl_certificate /home/<USER>/ssl_2022/lif.id-cloudflare-public.pem;
               #ssl_certificate_key /home/<USER>/ssl_2022/lif.id-cloudflare-private.key;

           ssl_certificate /home/<USER>/lif.fit.pem;
           ssl_certificate_key /home/<USER>/lif.fit.key;
}



logs docker-compose
=====================


************** - - [21/Sep/2023:11:04:35 +0000] "GET /ws?channelId=6179c09def817fbf7f478bf4ae0a90ba57c51149 HTTP/1.1" 101 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" "websocket" "Upgrade" "**************" 8ca757381fe17beaad4fa9b2d7edf107



logs kubernetes logs GKE ERROR
=====================

2023/09/21 11:18:31 [error] 3214#3214: *1665374 recv() failed (104: Connection reset by peer) while sending to client, client: *********, server: chat.lif.place, request: "GET /ws?channelId=6179c09def817fbf7f478bf4ae0a90ba57c51149 HTTP/1.1", upstream: "http://**********:8443/?channelId=6179c09def817fbf7f478bf4ae0a90ba57c51149", host: "chat.lif.place"

********* - - [21/Sep/2023:11:18:31 +0000] "GET /ws?channelId=6179c09def817fbf7f478bf4ae0a90ba57c51149 HTTP/1.1" 400 54 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 589 0.001 [stg-gorilla-443] [] **********:8443 48 0.001 400 a2c5fcf76465442602dbeb7e6d581686



logs using di ganti menggunakan path /ws/
==========================
*********** - - [24/Sep/2023:22:51:33 +0000] "GET /ws?channelId=6179c09def817fbf7f478bf4ae0a90ba57c51149 HTTP/1.1" 404 548 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 589 0.001 [upstream-default-backend] [] 127.0.0.1:8181 548 0.001 404 ff047d411712e166f49c4ed83a85ec9f


logs di ganti menggunakan path /ws/
===========================
*********** - - [24/Sep/2023:23:26:53 +0000] "GET /ws HTTP/1.1" 301 162 "-" "-" 155 0.000 [-] [] - - - - d6ec65eedc5d1c1ebe6ffcd8c6faed97








