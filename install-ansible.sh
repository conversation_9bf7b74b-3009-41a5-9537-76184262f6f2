#!/bin/bash

# Installing Ansible
echo "Installing Ansible..."

# Install Homebrew if not installed
which -s brew
if [[ $? != 0 ]] ; then
    echo "Homebrew not installed. Installing..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "Homebrew already installed. Updating..."
    brew update
fi

# Install ansible using Homebrew
brew install ansible

# Installing kubeseal
echo "Installing kubeseal..."

brew install kubeseal

echo "Installation complete!"
