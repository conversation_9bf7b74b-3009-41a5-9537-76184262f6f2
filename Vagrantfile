Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/bionic64" # Use a Ubuntu 18.04 base box
  config.vm.network "private_network", type: "dhcp"
  config.vm.provider "virtualbox" do |vb|
    vb.memory = "1024" # Set the amount of RAM for the VM
  end

  config.vm.provision "shell", inline: <<-SHELL
    # Update the package list and install PostgreSQL
    sudo apt-get update
    sudo apt-get install -y postgresql postgresql-contrib
    # Allow remote connections (not recommended for production)
    echo "host all all 0.0.0.0/0 md5" | sudo tee -a /etc/postgresql/12/main/pg_hba.conf
    echo "listen_addresses = '*'" | sudo tee -a /etc/postgresql/12/main/postgresql.conf
    sudo service postgresql restart
  SHELL
end
