[DEPRECATION WARNING]: [defaults]collections_paths option, does not fit var 
naming standard, use the singular form collections_path instead. This feature 
will be removed from ansible-core in version 2.19. Deprecation warnings can be 
disabled by setting deprecation_warnings=False in ansible.cfg.
[WARNING]: Invalid characters were found in group names but not replaced, use
-vvvv to see details
ansible-playbook [core 2.16.0]
  config file = /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/ansible.cfg
  configured module search path = ['/Users/<USER>/.ansible/plugins/modules', '/usr/share/ansible/plugins/modules']
  ansible python module location = /usr/local/lib/python3.11/site-packages/ansible
  ansible collection location = /usr/local/lib/python3.11/site-packages/ansible_collections
  executable location = /usr/local/bin/ansible-playbook
  python version = 3.11.5 (main, Aug 24 2023, 15:23:30) [Clang 14.0.0 (clang-1400.0.29.202)] (/usr/local/opt/python@3.11/bin/python3.11)
  jinja version = 3.1.2
  libyaml = True
Using /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/ansible.cfg as config file
setting up inventory plugins
Loading collection ansible.builtin from 
host_list declined parsing /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/inventory/hosts_prod as it did not pass its verify_file() method
script declined parsing /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/inventory/hosts_prod as it did not pass its verify_file() method
auto declined parsing /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/inventory/hosts_prod as it did not pass its verify_file() method
Not replacing invalid character(s) "{'-'}" in group name (internal-servers)
Not replacing invalid character(s) "{'-'}" in group name (internal-servers)
Not replacing invalid character(s) "{'-'}" in group name (internal-servers)
Not replacing invalid character(s) "{'-'}" in group name (internal-servers)
Parsed /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/inventory/hosts_prod inventory source with ini plugin
Loading callback plugin default of type stdout, v2.0 from /usr/local/lib/python3.11/site-packages/ansible/plugins/callback/default.py
redirecting (type: callback) ansible.builtin.timer to ansible.posix.timer
Loading collection ansible.posix from /usr/local/lib/python3.11/site-packages/ansible_collections/ansible/posix
redirecting (type: callback) ansible.builtin.profile_tasks to ansible.posix.profile_tasks
redirecting (type: callback) ansible.builtin.profile_roles to ansible.posix.profile_roles
Skipping callback 'default', as we already have a stdout callback.
Skipping callback 'minimal', as we already have a stdout callback.
Skipping callback 'oneline', as we already have a stdout callback.
Loading callback plugin ansible.posix.timer of type aggregate, v2.0 from /usr/local/lib/python3.11/site-packages/ansible_collections/ansible/posix/plugins/callback/timer.py
Loading callback plugin ansible.posix.profile_tasks of type aggregate, v2.0 from /usr/local/lib/python3.11/site-packages/ansible_collections/ansible/posix/plugins/callback/profile_tasks.py
Loading callback plugin ansible.posix.profile_roles of type aggregate, v2.0 from /usr/local/lib/python3.11/site-packages/ansible_collections/ansible/posix/plugins/callback/profile_roles.py

PLAYBOOK: tunneling.yaml *******************************************************
Positional arguments: playbooks/tunneling.yaml
verbosity: 4
private_key_file: /Users/<USER>/.ssh/lif
remote_user: ubuntu
connection: ssh
become_method: sudo
tags: ('all',)
inventory: ('/Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/inventory/hosts_prod',)
forks: 5
1 plays in playbooks/tunneling.yaml

PLAY [Execute SSH and SSHuttle commands] ***************************************

TASK [Gathering Facts] *********************************************************
task path: /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/playbooks/tunneling.yaml:2
Monday 27 November 2023  09:39:39 +0700 (0:00:00.022)       0:00:00.022 ******* 
Monday 27 November 2023  09:39:39 +0700 (0:00:00.020)       0:00:00.020 ******* 
<127.0.0.1> ESTABLISH LOCAL CONNECTION FOR USER: lookman
<127.0.0.1> EXEC /bin/sh -c 'echo ~lookman && sleep 0'
<127.0.0.1> EXEC /bin/sh -c '( umask 77 && mkdir -p "` echo /Users/<USER>/.ansible/tmp `"&& mkdir "` echo /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368 `" && echo ansible-tmp-1701052779.64396-77053-91235516684368="` echo /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368 `" ) && sleep 0'
Using module file /usr/local/lib/python3.11/site-packages/ansible/modules/setup.py
<127.0.0.1> PUT /Users/<USER>/.ansible/tmp/ansible-local-77051q7r5u_60/tmpxcsby0gm TO /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368/AnsiballZ_setup.py
<127.0.0.1> EXEC /bin/sh -c 'chmod u+x /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368/ /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368/AnsiballZ_setup.py && sleep 0'
<127.0.0.1> EXEC /bin/sh -c 'sudo -H -S -n  -u root /bin/sh -c '"'"'echo BECOME-SUCCESS-okagxcjdyakyeurslvxnmftsbarwekgl ; /usr/local/opt/python@3.11/bin/python3.11 /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368/AnsiballZ_setup.py'"'"' && sleep 0'
<127.0.0.1> EXEC /bin/sh -c 'rm -f -r /Users/<USER>/.ansible/tmp/ansible-tmp-1701052779.64396-77053-91235516684368/ > /dev/null 2>&1 && sleep 0'
ok: [localhost]

TASK [Run SSHuttle command] ****************************************************
task path: /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/playbooks/tunneling.yaml:6
Monday 27 November 2023  09:39:45 +0700 (0:00:06.286)       0:00:06.309 ******* 
Monday 27 November 2023  09:39:45 +0700 (0:00:06.286)       0:00:06.306 ******* 
<127.0.0.1> ESTABLISH LOCAL CONNECTION FOR USER: lookman
<127.0.0.1> EXEC /bin/sh -c 'echo ~lookman && sleep 0'
<127.0.0.1> EXEC /bin/sh -c '( umask 77 && mkdir -p "` echo /Users/<USER>/.ansible/tmp `"&& mkdir "` echo /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843 `" && echo ansible-tmp-1701052785.906924-77089-224048554385843="` echo /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843 `" ) && sleep 0'
Using module file /usr/local/lib/python3.11/site-packages/ansible/modules/command.py
<127.0.0.1> PUT /Users/<USER>/.ansible/tmp/ansible-local-77051q7r5u_60/tmpstxau7qw TO /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/AnsiballZ_command.py
<127.0.0.1> PUT /Users/<USER>/.ansible/tmp/ansible-local-77051q7r5u_60/tmpl2reuznr TO /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/async_wrapper.py
<127.0.0.1> EXEC /bin/sh -c 'chmod u+x /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/ /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/AnsiballZ_command.py /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/async_wrapper.py && sleep 0'
<127.0.0.1> EXEC /bin/sh -c 'sudo -H -S -n  -u root /bin/sh -c '"'"'echo BECOME-SUCCESS-qqiqudmvdjibzurfoztglysowfdusxje ; ANSIBLE_ASYNC_DIR='"'"'"'"'"'"'"'"'~/.ansible_async'"'"'"'"'"'"'"'"' /usr/local/opt/python@3.11/bin/python3.11 /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/async_wrapper.py j102898901882 120 /Users/<USER>/.ansible/tmp/ansible-tmp-1701052785.906924-77089-224048554385843/AnsiballZ_command.py _'"'"' && sleep 0'
changed: [localhost] => {
    "ansible_job_id": "j102898901882.77106",
    "changed": true,
    "finished": 0,
    "results_file": "/var/root/.ansible_async/j102898901882.77106",
    "started": 1
}

TASK [Run SSH command] *********************************************************
task path: /Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/playbooks/tunneling.yaml:12
Monday 27 November 2023  09:39:46 +0700 (0:00:00.927)       0:00:07.236 ******* 
Monday 27 November 2023  09:39:46 +0700 (0:00:00.927)       0:00:07.234 ******* 
<127.0.0.1> ESTABLISH LOCAL CONNECTION FOR USER: lookman
<127.0.0.1> EXEC /bin/sh -c 'echo ~lookman && sleep 0'
<127.0.0.1> EXEC /bin/sh -c '( umask 77 && mkdir -p "` echo /Users/<USER>/.ansible/tmp `"&& mkdir "` echo /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630 `" && echo ansible-tmp-1701052786.8564422-77113-148343146329630="` echo /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630 `" ) && sleep 0'
Using module file /usr/local/lib/python3.11/site-packages/ansible/modules/command.py
<127.0.0.1> PUT /Users/<USER>/.ansible/tmp/ansible-local-77051q7r5u_60/tmp2ktwx93x TO /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/AnsiballZ_command.py
<127.0.0.1> PUT /Users/<USER>/.ansible/tmp/ansible-local-77051q7r5u_60/tmpa20h0wft TO /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/async_wrapper.py
<127.0.0.1> EXEC /bin/sh -c 'chmod u+x /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/ /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/AnsiballZ_command.py /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/async_wrapper.py && sleep 0'
<127.0.0.1> EXEC /bin/sh -c 'sudo -H -S -n  -u root /bin/sh -c '"'"'echo BECOME-SUCCESS-lhmmmnqinvvopafcqkmcmfshwvhtoohc ; ANSIBLE_ASYNC_DIR='"'"'"'"'"'"'"'"'~/.ansible_async'"'"'"'"'"'"'"'"' /usr/local/opt/python@3.11/bin/python3.11 /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/async_wrapper.py j746826796823 120 /Users/<USER>/.ansible/tmp/ansible-tmp-1701052786.8564422-77113-148343146329630/AnsiballZ_command.py _'"'"' && sleep 0'
changed: [localhost] => {
    "ansible_job_id": "j746826796823.77130",
    "changed": true,
    "finished": 0,
    "results_file": "/var/root/.ansible_async/j746826796823.77130",
    "started": 1
}

PLAY RECAP *********************************************************************
localhost                  : ok=3    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   

Playbook run took 0 days, 0 hours, 0 minutes, 8 seconds
Monday 27 November 2023  09:39:47 +0700 (0:00:00.917)       0:00:08.153 ******* 
=============================================================================== 
Gathering Facts --------------------------------------------------------- 6.29s
/Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/playbooks/tunneling.yaml:2 
Run SSHuttle command ---------------------------------------------------- 0.93s
/Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/playbooks/tunneling.yaml:6 
Run SSH command --------------------------------------------------------- 0.92s
/Users/<USER>/Documents/Archive/LIF/lif-ansible/lif-ansible/playbooks/tunneling.yaml:12 
Monday 27 November 2023  09:39:47 +0700 (0:00:00.917)       0:00:08.151 ******* 
=============================================================================== 
gather_facts ------------------------------------------------------------ 6.29s
shell ------------------------------------------------------------------- 1.84s
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 
total ------------------------------------------------------------------- 8.13s
OpenSSH_8.6p1, LibreSSL 3.3.6
debug1: Reading configuration data /etc/ssh/ssh_config
debug1: /etc/ssh/ssh_config line 21: include /etc/ssh/ssh_config.d/* matched no files
debug1: /etc/ssh/ssh_config line 54: Applying options for *
debug1: Authenticator provider $SSH_SK_PROVIDER did not resolve; disabling
debug1: Connecting to ************** [**************] port 22.
debug1: Connection established.
debug1: identity file /Users/<USER>/.ssh/lif type 3
debug1: identity file /Users/<USER>/.ssh/lif-cert type -1
debug1: Local version string SSH-2.0-OpenSSH_8.6
debug1: Remote protocol version 2.0, remote software version OpenSSH_8.2p1 Ubuntu-4ubuntu0.9
debug1: compat_banner: match: OpenSSH_8.2p1 Ubuntu-4ubuntu0.9 pat OpenSSH* compat 0x04000000
debug1: Authenticating to **************:22 as 'lookman.af'
debug1: load_hostkeys: fopen /Users/<USER>/.ssh/known_hosts2: No such file or directory
debug1: load_hostkeys: fopen /etc/ssh/ssh_known_hosts: No such file or directory
debug1: load_hostkeys: fopen /etc/ssh/ssh_known_hosts2: No such file or directory
debug1: SSH2_MSG_KEXINIT sent
debug1: SSH2_MSG_KEXINIT received
debug1: kex: algorithm: curve25519-sha256
debug1: kex: host key algorithm: ssh-ed25519
debug1: kex: server->client cipher: <EMAIL> MAC: <implicit> compression: none
debug1: kex: client->server cipher: <EMAIL> MAC: <implicit> compression: none
debug1: expecting SSH2_MSG_KEX_ECDH_REPLY
debug1: SSH2_MSG_KEX_ECDH_REPLY received
debug1: Server host key: ssh-ed25519 SHA256:hTErBce6iRoUKh+MJwQkg+Th4LJsmXOYBPrP0ushRWQ
debug1: load_hostkeys: fopen /Users/<USER>/.ssh/known_hosts2: No such file or directory
debug1: load_hostkeys: fopen /etc/ssh/ssh_known_hosts: No such file or directory
debug1: load_hostkeys: fopen /etc/ssh/ssh_known_hosts2: No such file or directory
debug1: Host '**************' is known and matches the ED25519 host key.
debug1: Found key in /Users/<USER>/.ssh/known_hosts:405
debug1: rekey out after 134217728 blocks
debug1: SSH2_MSG_NEWKEYS sent
debug1: expecting SSH2_MSG_NEWKEYS
debug1: SSH2_MSG_NEWKEYS received
debug1: rekey in after 134217728 blocks
debug1: Will attempt key: <EMAIL> ED25519 SHA256:jTEeor0jrcWJxGCg1xMYdFcmXqob177E3DT1ok6JUOw agent
debug1: Will attempt key: /Users/<USER>/.ssh/lif ED25519 SHA256:3ftoBYH+8J+WkWLpDLJ4xkWbWnahl1N+Ue0lr49c6fs explicit
debug1: SSH2_MSG_EXT_INFO received
debug1: kex_input_ext_info: server-sig-algs=<ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>>
Starting sshuttle proxy (version 1.1.1).
c : which() could not find 'doas' in /opt/apache-maven/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/sbin/
c : which() found 'sudo' at /usr/bin/sudo
c : Starting firewall manager with command: ['/usr/bin/sudo', '-p', '[local sudo] Password: ', '/usr/bin/env', 'PYTHONPATH=/usr/local/Cellar/sshuttle/1.1.1/libexec/lib/python3.12/site-packages', '/usr/local/Cellar/sshuttle/1.1.1/libexec/bin/python', '/usr/local/bin/sshuttle', '-v', '-v', '--method', 'auto', '--firewall']
debug1: SSH2_MSG_SERVICE_ACCEPT received
debug1: Authentications that can continue: publickey
debug1: Next authentication method: publickey
debug1: Offering public key: <EMAIL> ED25519 SHA256:jTEeor0jrcWJxGCg1xMYdFcmXqob177E3DT1ok6JUOw agent
debug1: Authentications that can continue: publickey
debug1: Offering public key: /Users/<USER>/.ssh/lif ED25519 SHA256:3ftoBYH+8J+WkWLpDLJ4xkWbWnahl1N+Ue0lr49c6fs explicit
debug1: Server accepts key: /Users/<USER>/.ssh/lif ED25519 SHA256:3ftoBYH+8J+WkWLpDLJ4xkWbWnahl1N+Ue0lr49c6fs explicit
debug1: Authentication succeeded (publickey).
Authenticated to ************** ([**************]:22).
debug1: Local connections to LOCALHOST:15672 forwarded to remote address ********:15672
debug1: Local forwarding listening on 127.0.0.1 port 15672.
debug1: channel 0: new [port listener]
debug1: Local forwarding listening on ::1 port 15672.
debug1: channel 1: new [port listener]
debug1: Requesting <EMAIL>
debug1: Entering interactive session.
debug1: pledge: filesystem full
fw: Starting firewall with Python version 3.12.0
fw: which() could not find 'iptables' in /opt/apache-maven/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/sbin/
fw: nat method not supported because 'iptables' command is missing.
fw: which() could not find 'nft' in /opt/apache-maven/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/sbin/
fw: nft method not supported because 'nft' command is missing.
fw: which() found 'pfctl' at /sbin/pfctl
fw: which() found 'pfctl' at /sbin/pfctl
fw: ready method name pf.
c : Found DNS servers in /etc/resolv.conf: ['***********', '************', '***********']
c : IPv6 enabled: Using default IPv6 listen address ::1
c : Method: pf
c : IPv4: on
c : IPv6: on
c : UDP : off (not available with pf method)
c : DNS : on
c : User: off (not available with pf method)
c : Subnets to forward through remote host (type, IP, cidr mask width, startPort, endPort):
c :   (<AddressFamily.AF_INET: 2>, '********', 32, 0, 0)
c : Subnets to exclude from forwarding:
c :   (<AddressFamily.AF_INET: 2>, '127.0.0.1', 32, 0, 0)
c :   (<AddressFamily.AF_INET6: 30>, '::1', 128, 0, 0)
c : DNS requests normally directed at these servers will be redirected to remote:
c :   (<AddressFamily.AF_INET: 2>, '***********')
c :   (<AddressFamily.AF_INET: 2>, '************')
c :   (<AddressFamily.AF_INET: 2>, '***********')
c : Trying to bind redirector on port 12300
c : TCP redirector listening on ('::1', 12300, 0, 0).
c : TCP redirector listening with <socket.socket fd=6, family=30, type=1, proto=0, laddr=('::1', 12300, 0, 0)>.
c : TCP redirector listening on ('127.0.0.1', 12300).
c : TCP redirector listening with <socket.socket fd=8, family=2, type=1, proto=0, laddr=('127.0.0.1', 12300)>.
c : Trying to bind DNS redirector on port 12300
c : Trying to bind DNS redirector on port 12299
c : DNS listening on ('::1', 12299, 0, 0).
c : DNS listening with <socket.socket fd=9, family=30, type=2, proto=0, laddr=('::1', 12299, 0, 0)>.
c : DNS listening on ('127.0.0.1', 12299).
c : DNS listening with <socket.socket fd=10, family=2, type=2, proto=0, laddr=('127.0.0.1', 12299)>.
c : Starting client with Python version 3.12.0
c : Connecting to server...
c : which() found 'ssh' at /usr/bin/ssh
c : executing: ['/usr/bin/ssh', '-i', '~/.ssh/lif', 'lookman.af@**************', '--', '/bin/sh -c \'P=python3; $P -V 2>/dev/null || P=python; exec "$P" -c \'"\'"\'import sys, os; verbosity=2; sys.stdin = os.fdopen(0, "rb"); exec(compile(sys.stdin.read(1704), "assembler.py", "exec")); sys.exit(98);\'"\'"\'; exit 97\'']
debug1: client_input_global_request: rtype <EMAIL> want_reply 0
debug1: client_input_hostkeys: searching /Users/<USER>/.ssh/known_hosts for ************** / (none)
debug1: client_input_hostkeys: searching /Users/<USER>/.ssh/known_hosts2 for ************** / (none)
debug1: client_input_hostkeys: hostkeys file /Users/<USER>/.ssh/known_hosts2 does not exist
debug1: client_input_hostkeys: no new or deprecated keys from server
debug1: Remote: /home/<USER>/.ssh/authorized_keys:2: key options: agent-forwarding port-forwarding pty user-rc x11-forwarding
debug1: Remote: /home/<USER>/.ssh/authorized_keys:2: key options: agent-forwarding port-forwarding pty user-rc x11-forwarding
c :  > channel=0 cmd=PING len=7 (fullness=0)
 s: Running server on remote host with /usr/bin/python3 (version 3.8.10)
 s: assembling 'sshuttle' (88 bytes)
 s: assembling 'sshuttle.cmdline_options' (85 bytes)
 s: assembling 'sshuttle.helpers' (2853 bytes)
 s: assembling 'sshuttle.ssnet' (5811 bytes)
 s: assembling 'sshuttle.hostwatch' (2518 bytes)
 s: assembling 'sshuttle.server' (3786 bytes)
 s: latency control setting = True
 s:  > channel=0 cmd=PING len=7 (fullness=0)
 s: auto-nets:False
 s:  > channel=0 cmd=ROUTES len=0 (fullness=7)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=7/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 15/15
c : Connected to server.
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=7/0)
c :   Ready: 3 r=[12] w=[12] x=[]
c : <  channel=0 cmd=PING len=7
c :  > channel=0 cmd=PONG len=7 (fullness=7)
c : mux wrote: 15/15
c : mux wrote: 15/15
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=14/0)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=7/0)
 s:   Ready: 1 r=[] w=[1] x=[]
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=0 cmd=ROUTES len=0
fw: Got subnets: [(2, 32, False, '********', 0, 0), (2, 32, True, '127.0.0.1', 0, 0), (30, 128, True, '::1', 0, 0)]
fw: Got partial nslist: [(2, '***********')]
fw: Got partial nslist: [(2, '***********'), (2, '************')]
fw: Got partial nslist: [(2, '***********'), (2, '************'), (2, '***********')]
fw: Got nslist: [(2, '***********'), (2, '************'), (2, '***********')]
fw: Got ports: 12300,12300,12299,12299
fw: Got udp: False, user: None, tmark: 0x01, sshuttle_pid: 77217
fw: setting up.
fw: setting up IPv6.
fw: >> pfctl -s Interfaces -i lo -v
 s: mux wrote: 8/8
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=7/0)
fw: >> pfctl -s all
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=0 cmd=PING len=7
 s:  > channel=0 cmd=PONG len=7 (fullness=7)
 s: <  channel=0 cmd=PONG len=7
 s: received PING response
 s: mux wrote: 15/15
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=0/0)
fw: >> pfctl -a sshuttle6-12300 -f /dev/stdin
fw: >> pfctl -E
fw: setting up IPv4.
fw: >> pfctl -s Interfaces -i lo -v
fw: >> pfctl -s all
fw: >> pfctl -a sshuttle-12300 -f /dev/stdin
fw: >> pfctl -E
fw: which() could not find 'resolvectl' in /opt/apache-maven/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/sbin/
fw: which() could not find 'systemd-resolve' in /opt/apache-maven/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/sbin/
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=14/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=0 cmd=PONG len=7
c : received PING response
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=0/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 31051): 33 bytes
c :  > channel=1 cmd=DNS_REQ len=33 (fullness=0)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=33/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 41/41
c : DNS request from ('************', 13355): 33 bytes
c :  > channel=2 cmd=DNS_REQ len=33 (fullness=33)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=66/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=66/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=1 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=1.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=2 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=2.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 5] w=[] x=[] (fullness=0/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 103 bytes
 s:  > channel=1 cmd=DNS_RESPONSE len=103 (fullness=0)
 s: Waiting: 2 r=[0, 5] w=[1] x=[] (fullness=103/0)
 s:   Ready: 2 r=[] w=[1] x=[]
c :   Ready: 3 r=[10, 12] w=[] x=[]
c : <  channel=1 cmd=DNS_RESPONSE len=103
c : DNS request from ('************', 14838): 47 bytes
c :  > channel=3 cmd=DNS_REQ len=47 (fullness=66)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=113/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 55/55
c : DNS request from ('************', 59447): 47 bytes
c :  > channel=4 cmd=DNS_REQ len=47 (fullness=113)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=160/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 55/55
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=160/0)
 s: mux wrote: 111/111
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=103/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 101 bytes
 s:  > channel=2 cmd=DNS_RESPONSE len=101 (fullness=103)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=204/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 109/109
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=204/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=2 cmd=DNS_RESPONSE len=101
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=160/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=3 cmd=DNS_REQ len=47
 s: Incoming DNS request channel=3.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=4 cmd=DNS_REQ len=47
 s: Incoming DNS request channel=4.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 6] w=[] x=[] (fullness=204/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 63 bytes
 s:  > channel=3 cmd=DNS_RESPONSE len=63 (fullness=204)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=267/0)
 s:   Ready: 2 r=[6] w=[1] x=[]
 s: mux wrote: 71/71
 s: DNS response: 47 bytes
 s:  > channel=4 cmd=DNS_RESPONSE len=47 (fullness=267)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=314/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 55/55
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=314/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=3 cmd=DNS_RESPONSE len=63
c : <  channel=4 cmd=DNS_RESPONSE len=47
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=160/0)
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 2: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 3: new [direct-tcpip]
debug1: channel 2: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49825 to 127.0.0.1 port 15672, nchannels 4
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 58693): 47 bytes
c :  > channel=5 cmd=DNS_REQ len=47 (fullness=160)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=207/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 55/55
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=207/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 53829): 28 bytes
c :  > channel=6 cmd=DNS_REQ len=28 (fullness=207)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=235/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 36/36
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=235/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=5 cmd=DNS_REQ len=47
 s: Incoming DNS request channel=5.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=314/0)
 s:   Ready: 2 r=[0] w=[] x=[]
 s: <  channel=6 cmd=DNS_REQ len=28
 s: Incoming DNS request channel=6.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 5] w=[] x=[] (fullness=314/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 44 bytes
 s:  > channel=6 cmd=DNS_RESPONSE len=44 (fullness=314)
 s: Waiting: 2 r=[0, 4] w=[1] x=[] (fullness=358/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 52/52
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=6 cmd=DNS_RESPONSE len=44
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=235/0)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=358/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 98 bytes
 s:  > channel=5 cmd=DNS_RESPONSE len=98 (fullness=358)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=456/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 106/106
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=456/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=5 cmd=DNS_RESPONSE len=98
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=235/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 3049): 49 bytes
c :  > channel=7 cmd=DNS_REQ len=49 (fullness=235)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=284/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 57/57
c : DNS request from ('************', 51403): 49 bytes
c :  > channel=8 cmd=DNS_REQ len=49 (fullness=284)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=333/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 57/57
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=333/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=7 cmd=DNS_REQ len=49
 s: Incoming DNS request channel=7.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=8 cmd=DNS_REQ len=49
 s: Incoming DNS request channel=8.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=456/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 65 bytes
 s:  > channel=7 cmd=DNS_RESPONSE len=65 (fullness=456)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=521/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 73/73
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=521/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=7 cmd=DNS_RESPONSE len=65
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=333/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 49 bytes
 s:  > channel=8 cmd=DNS_RESPONSE len=49 (fullness=521)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=570/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 57/57
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=570/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=8 cmd=DNS_RESPONSE len=49
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=333/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 52078): 50 bytes
c :  > channel=9 cmd=DNS_REQ len=50 (fullness=333)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=383/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 58/58
c : DNS request from ('************', 39581): 50 bytes
c :  > channel=10 cmd=DNS_REQ len=50 (fullness=383)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=433/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 58/58
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=433/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=9 cmd=DNS_REQ len=50
 s: Incoming DNS request channel=9.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=570/0)
 s:   Ready: 2 r=[0] w=[] x=[]
 s: <  channel=10 cmd=DNS_REQ len=50
 s: Incoming DNS request channel=10.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 5] w=[] x=[] (fullness=570/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 173 bytes
 s:  > channel=9 cmd=DNS_RESPONSE len=173 (fullness=570)
 s: Waiting: 2 r=[0, 5] w=[1] x=[] (fullness=743/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 181/181
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=9 cmd=DNS_RESPONSE len=173
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=433/0)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=743/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 157 bytes
 s:  > channel=10 cmd=DNS_RESPONSE len=157 (fullness=743)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=900/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 165/165
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=900/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=10 cmd=DNS_RESPONSE len=157
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=433/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 51469): 42 bytes
c :  > channel=11 cmd=DNS_REQ len=42 (fullness=433)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=475/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 50/50
c : DNS request from ('************', 34161): 42 bytes
c :  > channel=12 cmd=DNS_REQ len=42 (fullness=475)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=517/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 50/50
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=517/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=11 cmd=DNS_REQ len=42
 s: Incoming DNS request channel=11.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=12 cmd=DNS_REQ len=42
 s: Incoming DNS request channel=12.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 6] w=[] x=[] (fullness=900/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 159 bytes
 s:  > channel=11 cmd=DNS_RESPONSE len=159 (fullness=900)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=1059/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 167/167
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=1059/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=11 cmd=DNS_RESPONSE len=159
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=517/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 63 bytes
 s:  > channel=12 cmd=DNS_RESPONSE len=63 (fullness=1059)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1122/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 71/71
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1122/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=12 cmd=DNS_RESPONSE len=63
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=517/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 49195): 48 bytes
c :  > channel=13 cmd=DNS_REQ len=48 (fullness=517)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=565/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 56/56
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=565/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=13 cmd=DNS_REQ len=48
 s: Incoming DNS request channel=13.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=1122/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 123 bytes
 s:  > channel=13 cmd=DNS_RESPONSE len=123 (fullness=1122)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1245/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 131/131
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1245/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=13 cmd=DNS_RESPONSE len=123
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=565/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 54828): 34 bytes
c :  > channel=14 cmd=DNS_REQ len=34 (fullness=565)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=599/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 42/42
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=599/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 61705): 47 bytes
c :  > channel=15 cmd=DNS_REQ len=47 (fullness=599)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=646/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 55/55
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=646/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=14 cmd=DNS_REQ len=34
 s: Incoming DNS request channel=14.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=1245/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 82 bytes
 s:  > channel=14 cmd=DNS_RESPONSE len=82 (fullness=1245)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1327/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 90/90
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1327/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=14 cmd=DNS_RESPONSE len=82
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=646/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=15 cmd=DNS_REQ len=47
 s: Incoming DNS request channel=15.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=1327/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 98 bytes
 s:  > channel=15 cmd=DNS_RESPONSE len=98 (fullness=1327)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1425/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 106/106
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1425/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=15 cmd=DNS_RESPONSE len=98
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=646/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 28618): 33 bytes
c :  > channel=16 cmd=DNS_REQ len=33 (fullness=646)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=679/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 41/41
c : DNS request from ('************', 25044): 33 bytes
c :  > channel=17 cmd=DNS_REQ len=33 (fullness=679)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=712/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=712/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=16 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=16.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=17 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=17.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=1425/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 49 bytes
 s:  > channel=16 cmd=DNS_RESPONSE len=49 (fullness=1425)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=1474/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 57/57
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=1474/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=16 cmd=DNS_RESPONSE len=49
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=712/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=17 cmd=DNS_RESPONSE len=33
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 33 bytes
 s:  > channel=17 cmd=DNS_RESPONSE len=33 (fullness=1474)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1507/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 41/41
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1507/0)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=712/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 18204): 37 bytes
c :  > channel=18 cmd=DNS_REQ len=37 (fullness=712)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=749/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 45/45
c : DNS request from ('************', 5996): 37 bytes
c :  > channel=19 cmd=DNS_REQ len=37 (fullness=749)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=786/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 45/45
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=786/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=18 cmd=DNS_REQ len=37
 s: Incoming DNS request channel=18.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=1507/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 53 bytes
 s:  > channel=18 cmd=DNS_RESPONSE len=53 (fullness=1507)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1560/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 61/61
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1560/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=19 cmd=DNS_REQ len=37
 s: Incoming DNS request channel=19.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=18 cmd=DNS_RESPONSE len=53
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=786/0)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=1560/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 37 bytes
 s:  > channel=19 cmd=DNS_RESPONSE len=37 (fullness=1560)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1597/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 45/45
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1597/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=19 cmd=DNS_RESPONSE len=37
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=786/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 56578): 33 bytes
c :  > channel=20 cmd=DNS_REQ len=33 (fullness=786)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=819/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=819/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=20 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=20.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=1597/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 113 bytes
 s:  > channel=20 cmd=DNS_RESPONSE len=113 (fullness=1597)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1710/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 121/121
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1710/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=20 cmd=DNS_RESPONSE len=113
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=819/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 50391): 34 bytes
c :  > channel=21 cmd=DNS_REQ len=34 (fullness=819)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=853/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 42/42
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=853/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=21 cmd=DNS_REQ len=34
 s: Incoming DNS request channel=21.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=1710/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 50 bytes
 s:  > channel=21 cmd=DNS_RESPONSE len=50 (fullness=1710)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1760/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 58/58
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1760/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=21 cmd=DNS_RESPONSE len=50
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=853/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 23039): 33 bytes
c :  > channel=22 cmd=DNS_REQ len=33 (fullness=853)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=886/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 41/41
c : DNS request from ('************', 19325): 33 bytes
c :  > channel=23 cmd=DNS_REQ len=33 (fullness=886)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=919/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=919/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=22 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=22.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=1760/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 129 bytes
 s:  > channel=22 cmd=DNS_RESPONSE len=129 (fullness=1760)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1889/0)
 s:   Ready: 1 r=[] w=[1] x=[]
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=22 cmd=DNS_RESPONSE len=129
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=919/0)
 s: mux wrote: 137/137
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1889/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=23 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=23.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=1889/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 33 bytes
 s:  > channel=23 cmd=DNS_RESPONSE len=33 (fullness=1889)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=1922/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 41/41
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=1922/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=23 cmd=DNS_RESPONSE len=33
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=919/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 60557): 40 bytes
c :  > channel=24 cmd=DNS_REQ len=40 (fullness=919)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=959/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 48/48
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=959/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=24 cmd=DNS_REQ len=40
 s: Incoming DNS request channel=24.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=1922/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 88 bytes
 s:  > channel=24 cmd=DNS_RESPONSE len=88 (fullness=1922)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=2010/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 96/96
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=2010/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=24 cmd=DNS_RESPONSE len=88
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=959/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 49239): 40 bytes
c :  > channel=25 cmd=DNS_REQ len=40 (fullness=959)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=999/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 48/48
c : DNS request from ('************', 53928): 40 bytes
c :  > channel=26 cmd=DNS_REQ len=40 (fullness=999)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1039/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 48/48
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1039/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=25 cmd=DNS_REQ len=40
 s: Incoming DNS request channel=25.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=26 cmd=DNS_REQ len=40
 s: Incoming DNS request channel=26.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=2010/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 169 bytes
 s:  > channel=25 cmd=DNS_RESPONSE len=169 (fullness=2010)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=2179/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 177/177
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=2179/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=25 cmd=DNS_RESPONSE len=169
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1039/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 64826): 42 bytes
c :  > channel=27 cmd=DNS_REQ len=42 (fullness=1039)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1081/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 50/50
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1081/0)
 s:   Ready: 2 r=[0] w=[] x=[]
 s: <  channel=27 cmd=DNS_REQ len=42
 s: Incoming DNS request channel=27.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 6] w=[] x=[] (fullness=2179/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 42 bytes
 s:  > channel=27 cmd=DNS_RESPONSE len=42 (fullness=2179)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=2221/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 50/50
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=2221/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=27 cmd=DNS_RESPONSE len=42
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1081/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 51540): 35 bytes
c :  > channel=28 cmd=DNS_REQ len=35 (fullness=1081)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1116/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 43/43
c : DNS request from ('************', 60904): 35 bytes
c :  > channel=29 cmd=DNS_REQ len=35 (fullness=1116)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1151/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 43/43
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1151/0)
 s:   Ready: 2 r=[0] w=[] x=[]
 s: <  channel=28 cmd=DNS_REQ len=35
 s: Incoming DNS request channel=28.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=29 cmd=DNS_REQ len=35
 s: Incoming DNS request channel=29.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 4 r=[0, 4, 5, 6] w=[] x=[] (fullness=2221/0)
 s:   Ready: 4 r=[5, 6] w=[] x=[]
 s: DNS response: 313 bytes
 s:  > channel=26 cmd=DNS_RESPONSE len=313 (fullness=2221)
 s: DNS response: 140 bytes
 s:  > channel=29 cmd=DNS_RESPONSE len=140 (fullness=2534)
 s: Waiting: 2 r=[0, 4] w=[1] x=[] (fullness=2674/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 321/321
 s: Waiting: 2 r=[0, 4] w=[1] x=[] (fullness=2674/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 148/148
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=26 cmd=DNS_RESPONSE len=313
c : <  channel=29 cmd=DNS_RESPONSE len=140
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1151/0)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=2674/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 124 bytes
 s:  > channel=28 cmd=DNS_RESPONSE len=124 (fullness=2674)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=2798/0)
 s:   Ready: 1 r=[] w=[1] x=[]
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 54223): 42 bytes
c :  > channel=30 cmd=DNS_REQ len=42 (fullness=1151)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1193/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 50/50
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1193/0)
 s: mux wrote: 132/132
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=2798/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=28 cmd=DNS_RESPONSE len=124
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1193/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 65183): 39 bytes
c :  > channel=31 cmd=DNS_REQ len=39 (fullness=1193)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1232/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 47/47
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1232/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=30 cmd=DNS_REQ len=42
 s: Incoming DNS request channel=30.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=2798/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 186 bytes
 s:  > channel=30 cmd=DNS_RESPONSE len=186 (fullness=2798)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=2984/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 194/194
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=30 cmd=DNS_RESPONSE len=186
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1232/0)
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=2984/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=31 cmd=DNS_REQ len=39
 s: Incoming DNS request channel=31.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=2984/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 39 bytes
 s:  > channel=31 cmd=DNS_RESPONSE len=39 (fullness=2984)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3023/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 47/47
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3023/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=31 cmd=DNS_RESPONSE len=39
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1232/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 2823): 33 bytes
c :  > channel=32 cmd=DNS_REQ len=33 (fullness=1232)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1265/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 41/41
c : DNS request from ('************', 54391): 33 bytes
c :  > channel=33 cmd=DNS_REQ len=33 (fullness=1265)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1298/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1298/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=32 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=32.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=33 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=33.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=3023/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 49 bytes
 s:  > channel=32 cmd=DNS_RESPONSE len=49 (fullness=3023)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=3072/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 57/57
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=3072/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=32 cmd=DNS_RESPONSE len=49
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1298/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 33 bytes
 s:  > channel=33 cmd=DNS_RESPONSE len=33 (fullness=3072)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3105/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 41/41
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3105/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=33 cmd=DNS_RESPONSE len=33
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1298/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 56692): 39 bytes
c :  > channel=34 cmd=DNS_REQ len=39 (fullness=1298)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1337/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 47/47
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1337/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=34 cmd=DNS_REQ len=39
 s: Incoming DNS request channel=34.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=3105/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 125 bytes
 s:  > channel=34 cmd=DNS_RESPONSE len=125 (fullness=3105)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3230/0)
 s:   Ready: 1 r=[] w=[1] x=[]
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=34 cmd=DNS_RESPONSE len=125
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1337/0)
 s: mux wrote: 133/133
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3230/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 51530): 39 bytes
c :  > channel=35 cmd=DNS_REQ len=39 (fullness=1337)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1376/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 47/47
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1376/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=35 cmd=DNS_REQ len=39
 s: Incoming DNS request channel=35.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=3230/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 55 bytes
 s:  > channel=35 cmd=DNS_RESPONSE len=55 (fullness=3230)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3285/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 63/63
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3285/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=35 cmd=DNS_RESPONSE len=55
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1376/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 64780): 35 bytes
c :  > channel=36 cmd=DNS_REQ len=35 (fullness=1376)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1411/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 43/43
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1411/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=36 cmd=DNS_REQ len=35
 s: Incoming DNS request channel=36.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=3285/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 67 bytes
 s:  > channel=36 cmd=DNS_RESPONSE len=67 (fullness=3285)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3352/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 75/75
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3352/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=36 cmd=DNS_RESPONSE len=67
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1411/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 61612): 53 bytes
c :  > channel=37 cmd=DNS_REQ len=53 (fullness=1411)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1464/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 61/61
c : DNS request from ('************', 4502): 53 bytes
c :  > channel=38 cmd=DNS_REQ len=53 (fullness=1464)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1517/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 61/61
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1517/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=37 cmd=DNS_REQ len=53
 s: Incoming DNS request channel=37.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=38 cmd=DNS_REQ len=53
 s: Incoming DNS request channel=38.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=3352/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 213 bytes
 s:  > channel=37 cmd=DNS_RESPONSE len=213 (fullness=3352)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=3565/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 221/221
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=3565/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=37 cmd=DNS_RESPONSE len=213
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1517/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 53 bytes
 s:  > channel=38 cmd=DNS_RESPONSE len=53 (fullness=3565)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3618/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 61/61
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3618/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=38 cmd=DNS_RESPONSE len=53
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1517/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 35951): 54 bytes
c :  > channel=39 cmd=DNS_REQ len=54 (fullness=1517)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1571/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 62/62
c : DNS request from ('************', 52967): 54 bytes
c :  > channel=40 cmd=DNS_REQ len=54 (fullness=1571)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1625/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 62/62
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1625/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=39 cmd=DNS_REQ len=54
 s: Incoming DNS request channel=39.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=40 cmd=DNS_REQ len=54
 s: Incoming DNS request channel=40.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 5] w=[] x=[] (fullness=3618/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 75 bytes
 s:  > channel=40 cmd=DNS_RESPONSE len=75 (fullness=3618)
 s: Waiting: 2 r=[0, 4] w=[1] x=[] (fullness=3693/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 83/83
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=3693/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=40 cmd=DNS_RESPONSE len=75
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1625/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 203 bytes
 s:  > channel=39 cmd=DNS_RESPONSE len=203 (fullness=3693)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=3896/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 211/211
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=39 cmd=DNS_RESPONSE len=203
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=3896/0)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1625/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 35119): 33 bytes
c :  > channel=41 cmd=DNS_REQ len=33 (fullness=1625)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1658/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 41/41
c : DNS request from ('************', 13401): 33 bytes
c :  > channel=42 cmd=DNS_REQ len=33 (fullness=1658)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1691/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1691/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=41 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=41.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=42 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=42.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=3896/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 113 bytes
 s:  > channel=41 cmd=DNS_RESPONSE len=113 (fullness=3896)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=4009/0)
 s:   Ready: 2 r=[] w=[1] x=[]
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=41 cmd=DNS_RESPONSE len=113
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1691/0)
 s: mux wrote: 121/121
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=4009/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 154 bytes
 s:  > channel=42 cmd=DNS_RESPONSE len=154 (fullness=4009)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4163/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 162/162
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4163/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=42 cmd=DNS_RESPONSE len=154
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1691/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 50402): 39 bytes
c :  > channel=43 cmd=DNS_REQ len=39 (fullness=1691)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1730/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 47/47
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1730/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=43 cmd=DNS_REQ len=39
 s: Incoming DNS request channel=43.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=4163/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 55 bytes
 s:  > channel=43 cmd=DNS_RESPONSE len=55 (fullness=4163)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4218/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 63/63
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4218/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=43 cmd=DNS_RESPONSE len=55
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1730/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 7250): 37 bytes
c :  > channel=44 cmd=DNS_REQ len=37 (fullness=1730)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1767/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 45/45
c : DNS request from ('************', 4308): 37 bytes
c :  > channel=45 cmd=DNS_REQ len=37 (fullness=1767)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1804/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 45/45
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1804/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=44 cmd=DNS_REQ len=37
 s: Incoming DNS request channel=44.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=45 cmd=DNS_REQ len=37
 s: Incoming DNS request channel=45.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 5, 6] w=[] x=[] (fullness=4218/0)
 s:   Ready: 3 r=[5] w=[] x=[]
 s: DNS response: 157 bytes
 s:  > channel=44 cmd=DNS_RESPONSE len=157 (fullness=4218)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=4375/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 165/165
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=4375/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=44 cmd=DNS_RESPONSE len=157
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1804/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 61 bytes
 s:  > channel=45 cmd=DNS_RESPONSE len=61 (fullness=4375)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4436/0)
 s:   Ready: 1 r=[] w=[1] x=[]
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=45 cmd=DNS_RESPONSE len=61
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1804/0)
 s: mux wrote: 69/69
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4436/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 32481): 39 bytes
c :  > channel=46 cmd=DNS_REQ len=39 (fullness=1804)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1843/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 47/47
c : DNS request from ('************', 20945): 39 bytes
c :  > channel=47 cmd=DNS_REQ len=39 (fullness=1843)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1882/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 47/47
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1882/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=46 cmd=DNS_REQ len=39
 s: Incoming DNS request channel=46.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=4436/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 55 bytes
 s:  > channel=46 cmd=DNS_RESPONSE len=55 (fullness=4436)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4491/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 63/63
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4491/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=46 cmd=DNS_RESPONSE len=55
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1882/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=47 cmd=DNS_REQ len=39
 s: Incoming DNS request channel=47.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=4491/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 39 bytes
 s:  > channel=47 cmd=DNS_RESPONSE len=39 (fullness=4491)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4530/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 47/47
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4530/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=47 cmd=DNS_RESPONSE len=39
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1882/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 36806): 33 bytes
c :  > channel=48 cmd=DNS_REQ len=33 (fullness=1882)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1915/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 41/41
c : DNS request from ('************', 41084): 33 bytes
c :  > channel=49 cmd=DNS_REQ len=33 (fullness=1915)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1948/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 41/41
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1948/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=48 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=48.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=49 cmd=DNS_REQ len=33
 s: Incoming DNS request channel=49.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 6] w=[] x=[] (fullness=4530/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 97 bytes
 s:  > channel=48 cmd=DNS_RESPONSE len=97 (fullness=4530)
 s: Waiting: 2 r=[0, 6] w=[1] x=[] (fullness=4627/0)
 s:   Ready: 2 r=[] w=[1] x=[]
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=48 cmd=DNS_RESPONSE len=97
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1948/0)
 s: mux wrote: 105/105
 s: Waiting: 2 r=[0, 6] w=[] x=[] (fullness=4627/0)
 s:   Ready: 2 r=[6] w=[] x=[]
 s: DNS response: 33 bytes
 s:  > channel=49 cmd=DNS_RESPONSE len=33 (fullness=4627)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4660/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 41/41
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4660/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=49 cmd=DNS_RESPONSE len=33
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=1948/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 48431): 42 bytes
c :  > channel=50 cmd=DNS_REQ len=42 (fullness=1948)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=1990/0)
c :   Ready: 3 r=[10] w=[12] x=[]
c : mux wrote: 50/50
c : DNS request from ('************', 26081): 42 bytes
c :  > channel=51 cmd=DNS_REQ len=42 (fullness=1990)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=2032/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 50/50
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=2032/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=50 cmd=DNS_REQ len=42
 s: Incoming DNS request channel=50.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: <  channel=51 cmd=DNS_REQ len=42
 s: Incoming DNS request channel=51.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 3 r=[0, 4, 5] w=[] x=[] (fullness=4660/0)
 s:   Ready: 3 r=[4] w=[] x=[]
 s: DNS response: 159 bytes
 s:  > channel=50 cmd=DNS_RESPONSE len=159 (fullness=4660)
 s: Waiting: 2 r=[0, 5] w=[1] x=[] (fullness=4819/0)
 s:   Ready: 2 r=[] w=[1] x=[]
 s: mux wrote: 167/167
 s: Waiting: 2 r=[0, 5] w=[] x=[] (fullness=4819/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=50 cmd=DNS_RESPONSE len=159
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=2032/0)
 s:   Ready: 2 r=[5] w=[] x=[]
 s: DNS response: 63 bytes
 s:  > channel=51 cmd=DNS_RESPONSE len=63 (fullness=4819)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4882/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 71/71
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=51 cmd=DNS_RESPONSE len=63
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=2032/0)
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4882/0)
c :   Ready: 3 r=[10] w=[] x=[]
c : DNS request from ('************', 56929): 46 bytes
c :  > channel=52 cmd=DNS_REQ len=46 (fullness=2032)
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[12] x=[] (fullness=2078/0)
c :   Ready: 3 r=[] w=[12] x=[]
c : mux wrote: 54/54
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=2078/0)
 s:   Ready: 1 r=[0] w=[] x=[]
 s: <  channel=52 cmd=DNS_REQ len=46
 s: Incoming DNS request channel=52.
 s: Found DNS servers in /etc/resolv.conf: ['**********']
 s: DNS: sending to '**********':53 (try 1)
 s: Waiting: 2 r=[0, 4] w=[] x=[] (fullness=4882/0)
 s:   Ready: 2 r=[4] w=[] x=[]
 s: DNS response: 62 bytes
 s:  > channel=52 cmd=DNS_RESPONSE len=62 (fullness=4882)
 s: Waiting: 1 r=[0] w=[1] x=[] (fullness=4944/0)
 s:   Ready: 1 r=[] w=[1] x=[]
 s: mux wrote: 70/70
 s: Waiting: 1 r=[0] w=[] x=[] (fullness=4944/0)
c :   Ready: 3 r=[12] w=[] x=[]
c : <  channel=52 cmd=DNS_RESPONSE len=62
c : Waiting: 3 r=[6, 8, 9, 10, 12] w=[] x=[] (fullness=2078/0)
 s:   Ready: 1 r=[0] w=[] x=[]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 2: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 4: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 5: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 6: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 7: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 8: new [direct-tcpip]
debug1: channel 2: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49841 to 127.0.0.1 port 15672, nchannels 9
debug1: channel 4: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49842 to 127.0.0.1 port 15672, nchannels 8
debug1: channel 5: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49843 to 127.0.0.1 port 15672, nchannels 7
debug1: channel 6: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49844 to 127.0.0.1 port 15672, nchannels 6
debug1: channel 7: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49845 to 127.0.0.1 port 15672, nchannels 5
debug1: channel 8: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49846 to 127.0.0.1 port 15672, nchannels 4
debug1: channel 3: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from 127.0.0.1 port 49826 to 127.0.0.1 port 15672, nchannels 3
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 2: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 3: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 4: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 5: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 6: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 7: new [direct-tcpip]
debug1: channel 2: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49848 to ::1 port 15672, nchannels 8
debug1: channel 3: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49849 to ::1 port 15672, nchannels 7
debug1: channel 4: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49850 to ::1 port 15672, nchannels 6
debug1: channel 5: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49851 to ::1 port 15672, nchannels 5
debug1: channel 6: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49852 to ::1 port 15672, nchannels 4
debug1: channel 7: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49853 to ::1 port 15672, nchannels 3
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 2: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 3: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 4: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 5: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 6: new [direct-tcpip]
debug1: Connection to port 15672 forwarding to ******** port 15672 requested.
debug1: channel 7: new [direct-tcpip]
debug1: channel 4: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49856 to ::1 port 15672, nchannels 8
debug1: channel 7: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49859 to ::1 port 15672, nchannels 7
debug1: channel 2: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49854 to ::1 port 15672, nchannels 6
debug1: channel 3: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49855 to ::1 port 15672, nchannels 5
debug1: channel 5: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49857 to ::1 port 15672, nchannels 4
debug1: channel 6: free: direct-tcpip: listening port 15672 for ******** port 15672, connect from ::1 port 49858 to ::1 port 15672, nchannels 3
debug1: client_input_global_request: rtype <EMAIL> want_reply 1
debug1: client_input_global_request: rtype <EMAIL> want_reply 1
debug1: client_input_global_request: rtype <EMAIL> want_reply 1
