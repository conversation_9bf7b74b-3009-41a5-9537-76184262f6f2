[defaults]
collections_paths = ~/.ansible/collections
#collections_paths = /usr/local/lib/python3.11/site-packages/ansible_collections
#vault_password_file = ~/.vault_pass.txt
inventory = ./inventory/hosts_prod.ini
#become = true
remote_user = ubuntu 
private_key_file = ~/.ssh/lif.pem
#become_user = root
host_key_checking = False
callbacks_enabled = timer, profile_tasks, profile_roles
#gather_facts: False
#ignore_errors: yes
