# Define the GKE cluster
resource "google_container_cluster" "gke_cluster" {
  name               = "lif-gke-cluster"
  location           = "asia-southeast2"
  initial_node_count = 1
  network            = google_compute_network.lif_vpc_prod.name
  subnetwork         = google_compute_subnetwork.private_subnet.name

  node_config {
    machine_type = "e2-medium"
    disk_size_gb = 30
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
    ]

    tags = ["gke-node", "private-cluster"]
  }

  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }

  ip_allocation_policy {
    # IP aliases are enabled by default when this block is present
  }

  private_cluster_config {
    enable_private_endpoint = true
    enable_private_nodes    = true
    master_ipv4_cidr_block  = "**********/28"
  }

  depends_on = [
    google_compute_network.lif_vpc_prod,
    google_compute_subnetwork.private_subnet
  ]
}

# Define a node pool for the GKE cluster
resource "google_container_node_pool" "primary_preemptible_nodes" {
  name       = "primary-preemptible-nodes"
  location   = "asia-southeast2"
  cluster    = google_container_cluster.gke_cluster.name
  node_count = 1

  node_config {
    preemptible  = true
    machine_type = "e2-medium"
    disk_size_gb = 30

    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
    ]

    tags = ["gke-node", "preemptible"]
  }

  management {
    auto_repair = true
  }

  autoscaling {
    min_node_count = 1
    max_node_count = 3
  }

  depends_on = [
    google_container_cluster.gke_cluster
  ]
}
