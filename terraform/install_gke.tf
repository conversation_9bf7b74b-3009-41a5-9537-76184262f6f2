resource "google_container_cluster" "gke_cluster" {
  name     = "lif-prod-cluster-1"
  location = "asia-southeast2-a"
  project  = "lif-prod"

  network    = "projects/lif-prod/global/networks/lif-vpc-prod"
  subnetwork = "projects/lif-prod/regions/asia-southeast2/subnetworks/public-subnet"

  release_channel {
    channel = "REGULAR"
  }

  remove_default_node_pool = true
  initial_node_count       = 1

  enable_shielded_nodes = true
  deletion_protection   = true

  ip_allocation_policy {
    use_ip_aliases                = true
    cluster_secondary_range_name  = "gke-lif-prod-cluster-1-pods-853ee8c2"
    services_secondary_range_name = "gke-lif-prod-cluster-1-services-853ee8c2"
  }

  master_authorized_networks_config {
    enabled = true
    cidr_blocks = [
      {
        cidr_block   = "********/24"
        display_name = "private"
      },
      {
        cidr_block   = "********/24"
        display_name = "public"
      },
      {
        cidr_block   = "************/32"
        display_name = "lookman"
      },
      {
        cidr_block   = "************/32"
        display_name = "roy"
      },
      {
        cidr_block   = "*************/32"
        display_name = "lookman-fm"
      }
    ]
    gcp_public_cidrs_access_enabled     = true
    private_endpoint_enforcement_enabled = false
  }

  logging_config {
    enable_components = ["SYSTEM_COMPONENTS", "WORKLOADS"]
  }

  monitoring_config {
    enable_components = ["SYSTEM_COMPONENTS"]
  }

  addons_config {
    gce_persistent_disk_csi_driver_config {
      enabled = true
    }

    horizontal_pod_autoscaling {
      disabled = false
    }

    http_load_balancing {
      disabled = false
    }

    kubernetes_dashboard {
      disabled = true
    }

    network_policy_config {
      disabled = true
    }

    gcs_fuse_csi_driver_config {
      enabled = false
    }

    dns_cache_config {
      enabled = false
    }
  }

  datapath_provider = "LEGACY_DATAPATH"
}
