# Define a custom image from your snapshot
resource "google_compute_image" "custom_image" {
  name        = "base-images-lif-prod-2023-ubuntu22-04-25"
  description = "Custom image created from my-custom-snapshot"
  source_image = "projects/lif-prod/regions/asia-southeast2/snapshots/base-images-lif-prod-2023-ubuntu22-04"

}

# Redis Server
resource "google_compute_instance" "redis_server" {
  name         = "lif-prod-redis"
  machine_type = "e2-custom-4-4096"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh", "allow-icmp"]

  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = google_compute_image.custom_image.name
      size  = 50  # Increase the size of the boot disk to 50 GB, adjust as needed
    }
  }

  network_interface {
    network    = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.private_subnet.name
    network_ip = "********"
  }
}

# RabbitMQ Server
resource "google_compute_instance" "rabbitmq_server" {
  name         = "lif-prod-rabbitmq"
  machine_type = "e2-custom-4-4096"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh", "allow-icmp"]

  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = google_compute_image.custom_image.name
      size  = 20  # Increase the size of the boot disk to 80 GB, adjust as needed
    }
  }

  network_interface {
    network    = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.private_subnet.name
    network_ip = "********"
  }
}

# MongoDB Server
resource "google_compute_instance" "mongodb_server" {
  name         = "lif-prod-mongodb"
  machine_type = "e2-custom-4-4096"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh", "allow-icmp"]

  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = google_compute_image.custom_image.name
      size  = 50  # Increase the size of the boot disk to 120 GB, adjust as needed
    }
  }

  network_interface {
    network    = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.private_subnet.name
    network_ip = "********"
  }
}

# PostgreSQL Server
resource "google_compute_instance" "postgres_server" {
  name         = "lif-prod-postgres"
  machine_type = "e2-custom-4-4096"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh", "allow-icmp"]

  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = google_compute_image.custom_image.name
      size  = 50  # Increase the size of the boot disk to 100 GB, adjust as needed
    }
  }

  network_interface {
    network    = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.private_subnet.name
    network_ip = "********"
  }
}

