resource "google_compute_instance" "ubuntu_server" {
  name         = "lif-prod-ubuntu-base"
  machine_type = "e2-micro"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh", "allow-icmp"]

  boot_disk {
    initialize_params {
      image = "ubuntu-2204-lts"  # Use the public image for Ubuntu 22.04 LTS
      
    }
  }

  network_interface {
    network    = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.private_subnet.name
  }
}
