#!/bin/bash

# PostgreSQL login details
login_user="postgres"
login_password=""
login_host="********"

# Backup directory
backup_directory="/home/<USER>/tmp/"

# Get the current date and year
current_date=$(date +%Y-%m-%d)
current_year=$(date +%Y)


# Export the PostgreSQL password so that pg_dump doesn't prompt for it
export PGPASSWORD=$login_password

# Get a list of all databases
databases=$(psql -U $login_user -h $login_host -t -c "SELECT datname FROM pg_database WHERE datistemplate = false;")

# Loop through each database and dump it
for db in $databases; do
   psql -U $login_user -h $login_host -d $db -c "SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = '$db' AND pid <> pg_backend_pid() AND state = 'idle';"
   if [[ $db == lif* ]]; then
   pg_dump --serializable-deferrable -U $login_user -h $login_host -F c -b -f "$backup_directory/${db}-backup-$current_date.dump" $db
   fi
done


# Compress backup files
tar_file="$backup_directory/backup-$current_date.tar.gz"
tar -czvf "$tar_file" -C "$backup_directory" .


# Upload the tar file to GCS
for i in {1..5}; do
  gcloud alpha storage cp "$tar_file" "gs://lif-backup-postgresql/$current_year" && break || sleep 15
done


# i want to cleanup the backup directory after the backup is uploaded to gcs
rm -rf "$backup_directory"/*

# Unset the PostgreSQL password
unset PGPASSWORD