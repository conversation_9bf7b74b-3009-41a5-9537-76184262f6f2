---
- name: Backup and Upload MongoDB to GCS
  hosts: mongodb
  vars:
    backup_dir: /tmp/mongo_backups
    db_name: lif  # Replace with your actual database name
    timestamp: "{{ lookup('pipe', 'date +%Y%m%d%H%M%S') }}"
    backup_file: "{{ backup_dir }}/mongo_backup_{{ db_name }}_{{ timestamp }}.gz"
    bucket_name: lif-backup-mongodb  # Replace with your GCS bucket name
    current_year: "{{ lookup('pipe', 'date +%Y') }}"  # Get the current year
    gcs_backup_path: "{{ current_year }}"  # Use the current year variable

  tasks:
    - name: Ensure local backup directory exists
      file:
        path: "{{ backup_dir }}"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: '0755'

    - name: Perform MongoDB backup
      command: "mongodump --db {{ db_name }} --archive={{ backup_file }} --gzip"
      register: backup_result

    - name: Fail if MongoDB backup was unsuccessful
      fail:
        msg: "MongoDB backup failed!"
      when: backup_result.rc != 0

    - name: Upload MongoDB backup to GCS
      shell: "gsutil cp {{ backup_file }} gs://{{ bucket_name }}/{{ gcs_backup_path }}/"
      register: upload_result

    - name: Fail if upload to GCS was unsuccessful
      fail:
        msg: "Failed to upload MongoDB backup to GCS!"
      when: upload_result.rc != 0

    - name: Notify success
      debug:
        msg: "MongoDB backup was successfully created and uploaded to GCS at gs://{{ bucket_name }}/{{ gcs_backup_path }}/{{ backup_file | basename }}"
