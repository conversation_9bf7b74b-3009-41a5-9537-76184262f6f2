#!/bin/bash

# Configuration
PGUSER="your_user"
PGPASSWORD="your_password"
PGHOST="127.0.0.1"  # use Cloud SQL proxy or direct IP
export PGPASSWORD

BACKUP_DIR="./postgres_backups/2025-06-11_18-30-00"

for dump in "$BACKUP_DIR"/*.dump; do
  dbname=$(basename "$dump" _full.dump)
  echo "▶️ Restoring database: $dbname"

  # Drop database if exists
  echo "🔄 Dropping existing database if exists: $dbname"
  psql -h "$PGHOST" -U "$PGUSER" -tc "SELECT 1 FROM pg_database WHERE datname = '$dbname'" | grep -q 1 && \
    psql -h "$PGHOST" -U "$PGUSER" -c "DROP DATABASE \"$dbname\";"

  # Create database
  echo "🆕 Creating database: $dbname"
  psql -h "$PGHOST" -U "$PGUSER" -c "CREATE DATABASE \"$dbname\";"

  # Restore dump
  echo "📦 Restoring data to $dbname"
  pg_restore -h "$PGHOST" -U "$PGUSER" -d "$dbname" -Fc "$dump"

  echo "✅ Done restoring $dbname"
done

unset PGPASSWORD
echo "🎉 All databases restored."
