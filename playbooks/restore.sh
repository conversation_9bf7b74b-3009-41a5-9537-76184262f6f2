#!/bin/bash

# PostgreSQL login details
login_user="postgres"
#login_password=""
login_host="localhost"

# Backup directory
backup_directory="/home/<USER>/tmp/"

# Get the date of the previous day and the current year
previous_date=$(date -d "-1 day" +%Y-%m-%d)
current_year=$(date +%Y)
#previous_date=$(date +%Y-%m-%d)


# Export the PostgreSQL password so that pg_dump doesn't prompt for it
export PGPASSWORD=$login_password

# Download the backup file from GCS
gsutil cp "gs://lif-backup-postgresql/$current_year/backup-$previous_date.tar.gz" "$backup_directory" || exit 1

# Extract the backup file
tar -xzvf "$backup_directory/backup-$previous_date.tar.gz" -C "$backup_directory" || exit 1

# Loop through each database and restore it
for db in $(ls "$backup_directory" | grep -oP '.*(?=-backup)'); do
  pg_restore -U $login_user -h $login_host -d $db -F c -C "$backup_directory/${db}-backup-$previous_date.dump"
done

# Cleanup the backup directory after the restore is done
rm -rf "$backup_directory/*"

# Unset the PostgreSQL password
unset PGPASSWORD


# lif_health-backup-2024-03-16.dump
# lif_leaderboard-backup-2024-03-16.dump
# lif_notification-backup-2024-03-16.dump
# lif_teamtalk-backup-2024-03-16.dump
# lif_badge-backup-2024-03-16.dump
# lif_exp-backup-2024-03-16.dump
# lif_point-backup-2024-03-16.dump
# lif_reward-backup-2024-03-16.dump
# lif_survey-backup-2024-03-16.dump
# lif_user-backup-2024-03-16.dump
#  lif_challenge-backup-2024-03-16.dump
#  lif_content-backup-2024-03-16.dump
# lif_subscription-backup-2024-03-16.dump