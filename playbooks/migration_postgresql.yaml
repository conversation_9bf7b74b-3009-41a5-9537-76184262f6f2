---
- hosts: staging-gcp
  become: yes
  vars_files:
    - ../groups_vars/all/vault
  vars:
    migration_file_path_local: '../templates/tos.sql'
    migration_directory_remote: '/home/<USER>'
    migration_file_path_remote: "{{ migration_directory_remote }}/tos.sql"

  tasks:

    - name: Ensure migration directory exists on remote host
      file:
        path: "{{ migration_directory_remote }}"
        state: directory
        mode: '0755'

    - name: Copy migration file to remote host
      copy:
        src: "{{ migration_file_path_local }}"
        dest: "{{ migration_file_path_remote }}"
        mode: '0644'

    - name: Ensure PostgreSQL client is installed
      apt:
        name: postgresql-client
        state: present
      when: ansible_os_family == 'Debian'

    - name: Execute PostgreSQL migration using shell module
      shell: |
        PGPASSWORD='{{ postgres_password }}' psql -U postgres -h *********** -d lif_content -a -f "{{ migration_file_path_remote }}"
      register: psql_migration_result
      ignore_errors: yes

    - name: Display the output of the migration
      debug:
        var: psql_migration_result.stdout_lines
