---
- name: Create GCP build triggers for multiple repositories
  hosts: localhost
  gather_facts: no
  vars:
    gcp_project_id: lif-prod
    branch_name: master
    cd_branch: master
    repositories:
      - { repo: 'livingfitness/anvil', name: 'anvil', description: 'Auth Service' }
      - { repo: 'livingfitness/attendre', name: 'attendre', description: 'EXP worker' }
      - { repo: 'livingfitness/avis', name: 'avis', description: 'Notification service' }
      - { repo: 'livingfitness/bovidae', name: 'bovidae', description: 'Leaderboard worker' }
      - { repo: 'livingfitness/capra', name: 'capra', description: 'Leaderboard service' }
      - { repo: 'livingfitness/centaur', name: 'centaur', description: 'LIF API Gateway' }
      - { repo: 'livingfitness/coceco', name: 'coceco', description: 'Challenge service' }
      - { repo: 'livingfitness/compositeur', name: 'compositeur', description: 'Composer worker' }
      - { repo: 'livingfitness/core', name: 'core', description: 'Fitness data service' }
      - { repo: 'livingfitness/davinci', name: 'davinci', description: 'Storage service' }
      - { repo: 'livingfitness/estatico', name: 'estatico', description: 'Static webhost' }
      - { repo: 'livingfitness/expediteur', name: 'expediteur', description: 'Sender worker' }
      - { repo: 'livingfitness/indiquer', name: 'indiquer', description: 'Point service' }
      - { repo: 'livingfitness/lif-badge-service', name: 'lif-badge', description: 'Exp service' }
      - { repo: 'livingfitness/if-chat', name: 'lifchat', description: 'chat-service' }
      - { repo: 'livingfitness/lif_exp_service', name: 'lifexp', description: 'Exp service' }
      - { repo: 'livingfitness/lif_reward_service', name: 'lifreward', description: 'LIF reward service' }
      - { repo: 'livingfitness/ouvrier', name: 'ouvrier', description: 'Badge woker' }
      - { repo: 'livingfitness/paradisiaca', name: 'paradisiaca', description: 'Badge woker' }
      - { repo: 'livingfitness/lif_chall_frontend', name: 'portal', description: 'portal' }
      - { repo: 'livingfitness/recevoir', name: 'recevoir', description: 'Points worker' }
      - { repo: 'livingfitness/risala', name: 'risala', description: 'Chat service' }
      - { repo: 'livingfitness/stump', name: 'stump', description: 'User service' }
      - { repo: 'livingfitness/symvasi', name: 'symvasi', description: 'Subscription service' }
    tags:
      - master
  tasks:
    - name: Check existing triggers
      command: gcloud beta builds triggers list --project={{ gcp_project_id }} --format=json
      register: existing_triggers
    - name: Parse existing trigger names
      set_fact:
        existing_trigger_names: "{{ existing_triggers.stdout | from_json | map(attribute='name') | list }}"
    - name: Create build trigger for each repository
      command: >
        gcloud beta builds triggers create cloud-source-repositories
        --project={{ gcp_project_id }}
        --repo={{ item.repo }}
        --branch-pattern={{ branch_name }}
        --build-config=cloudbuild.yaml
        --description="{{ item.description }}"
        --name="{{ item.name }}"
        --substitutions=_CD_BRANCH={{ cd_branch }}
      loop: "{{ repositories }}"
      when: item.name not in existing_trigger_names

