---
- name: Deploy Ingress Nginx with Helm
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Add Ingress Nginx Helm repository
      command: helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
      ignore_errors: yes  # Ignore errors in case the repository is already added

    - name: Update Helm repositories
      command: helm repo update

    - name: Install Ingress Nginx Helm chart
      command: helm install nginx-ingress ingress-nginx/ingress-nginx -n nginx
      args:
        creates: nginx-ingress  # Skip installation if the release already exists

      register: helm_install_result
      ignore_errors: yes  # Helm install may fail if the release already exists

    - name: Debug Helm installation result
      debug:
        var: helm_install_result
