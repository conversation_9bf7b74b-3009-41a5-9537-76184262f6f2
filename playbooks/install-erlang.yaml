---
- name: Install Erlang on Ubuntu 22.04
  hosts: rabbitmq
  become: yes

  tasks:
    - name: Download Erlang Solutions repository package
      ansible.builtin.get_url:
        url: https://packages.erlang-solutions.com/erlang-solutions_2.0_all.deb
        dest: /tmp/erlang-solutions_2.0_all.deb
        mode: '0644'

    - name: Add Erlang Solutions repository
      ansible.builtin.apt_repository:
        repo: "deb [signed-by=/usr/share/keyrings/erlang-solutions-archive-keyring.gpg] https://packages.erlang-solutions.com/ubuntu jammy contrib"
        state: present

    - name: Import Erlang Solutions GPG key
      ansible.builtin.apt_key:
        url: "https://packages.erlang-solutions.com/ubuntu/erlang_solutions.asc"
        state: present

    - name: Update apt cache
      ansible.builtin.apt:
        update_cache: yes

    - name: Install esl-erlang
      ansible.builtin.apt:
        name: esl-erlang
        state: latest
        update_cache: yes
