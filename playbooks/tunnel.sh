#!/bin/bash

# Run SSHuttle command in the background
nohup sshuttle -e "ssh -i ~/.ssh/lif.pem" -r lookman.af@************** --dns -vvr lookman.af@************** ******** &
# u can acess http://********:15672/


# Run SSH command in the background 
nohup ssh -i ~/.ssh/lif.pem -L 15672:********:15672 lookman.af@************** -N -v &

# u can access  http://localhost:15672/


nohup ssh -i ~/.ssh/lif.pem -L 31165:********:31165 lookman.af@************** -N -v &


# Run SSHuttle command in the background AWX
#nohup sshuttle -e "ssh -i ~/.ssh/lif.pem" -r lookman.af@************** --dns -vvr lookman.af@************** ******** &
sshuttle --dns -r username@************** ********/24


sshuttle -e "ssh -i ~/.ssh/me" --dns -r lookman.af@************** ********

# u can acess http://********:31165/

# ssh -i ~/.ssh/lif.pem -vvv lookman.af@**************
# ssh -i ~/.ssh/lif.pem lookman.af@**************
# ssh -i ~/.ssh/lif.pem ubuntu@*************
# psql -h localhost -p 5432 -U postgres -d postgres
psql -h localhost -p 5432 -U postgres -d postgres
pg_dumpall -U postgres  > alldatabases_staging_backup-15ma
ret2024.sql

# dont using gsutil for uploading to google bucket just using gcloud storage cp
# gcloud auth login --no-launch-browser

# Credentials saved to file: [/home/<USER>/.config/gcloud/application_default_credentials.json]
#These credentials will be used by any library that requests Application Default Credentials (ADC).
#Quota project "lif-prod" was added to ADC which can be used by Google client libraries for billing and quota. Note that some services may still bill the project owning the resource.

step restore backup roles and permission postgresql

1 load backup roles globales.sql , restore to destination
2 run script restore.sh