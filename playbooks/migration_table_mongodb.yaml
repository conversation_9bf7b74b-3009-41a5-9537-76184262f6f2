---
- name: Add field to all documents in a MongoDB collection
  hosts: staging-gcp
  vars:
    mongodb_host: "your.mongodb.host"
    mongodb_port: 27017
    mongodb_database: "your_database"
    mongodb_collection: "your_collection"
    mongodb_user: "your_user"
    mongodb_pass: "your_password"
    new_field_name: "newFieldName"
    new_field_value: "defaultValue"

  tasks:
    - name: Add a new field to all documents in the collection
      community.mongodb.mongodb_shell:
        login_user: "{{ mongodb_user }}"
        login_password: "{{ mongodb_pass }}"
        login_host: "{{ mongodb_host }}"
        login_port: "{{ mongodb_port }}"
        eval: "db.{{ mongodb_collection }}.updateMany({}, {$set: {{ new_field_name }}: '{{ new_field_value }}'})"
      run_once: true
