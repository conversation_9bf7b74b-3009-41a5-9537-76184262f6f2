#!/bin/bash

# PostgreSQL login details
login_user="postgres"
login_password=""
login_host="localhost"

# Backup directory
backup_directory="/home/<USER>/tmp"

# Get the date of the previous day and the current year
previous_date=$(date -d "-1 day" +%Y-%m-%d)
current_year=$(date +%Y)
#previous_date=$(date +%Y-%m-%d)

# Array of database names
#databases=("lif_badge" "lif_challenge" "lif_content" "lif_exp" "lif_health" "lif_leaderboard" "lif_notification" "lif_point" "lif_reward" "lif_subscription" "lif_survey" "lif_teamtalk" "lif_user")

# Export the PostgreSQL password so that pg_dump doesn't prompt for it
export PGPASSWORD=$login_password

# Download the backup file from GCS
gsutil cp "gs://lif-backup-postgresql/$current_year/backup-$previous_date.tar.gz" "$backup_directory" || exit 1

# Extract the backup file
tar -xzvf "$backup_directory/backup-$previous_date.tar.gz" -C "$backup_directory" || exit 1

# Loop through each database and restore it
#for db in "${databases[@]}"; do
for db in $(ls "$backup_directory" | grep -oP '.*(?=-backup)'); do
  # DROP the database
   psql -U $login_user -h $login_host -c "DROP DATABASE IF EXISTS \"${db}\";"  
  
   # Create a new database
  psql -U $login_user -h $login_host -c "CREATE DATABASE \"${db}\";"

# Restore the database
  pg_restore -U $login_user -h $login_host -d "${db}" -F c "$backup_directory/${db}-backup-$previous_date.dump" || exit 1
done

# Cleanup the backup directory after the restore is done
rm -rf "$backup_directory"/*

# Unset the PostgreSQL password
unset PGPASSWORD