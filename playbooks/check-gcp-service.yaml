---
- name: Backup all PostgreSQL databases and upload to Google Cloud Storage
  hosts: postgres
  vars:
    gcp_service_account_key: /home/<USER>/gcp_service_account_key.json

  tasks:

    # - name: Authenticate with Google Cloud
    #   shell: gcloud auth activate-service-account --key-file="{{ gcp_service_account_key }}"
    #   environment:
    #     GOOGLE_APPLICATION_CREDENTIALS: "{{ gcp_service_account_key }}"
    #   args:
    #     executable: /bin/bash  

    # - name: Check active account
    #   shell: gcloud config get-value account
    #   register: active_account
    #   changed_when: false

    # - name: Display active account
    #   debug:
    #     msg: "{{ active_account.stdout }}"
    
    - name: Authenticate with Google Cloud
      ansible.builtin.command: /usr/bin/gcloud auth activate-service-account --key-file="{{ gcp_service_account_key }}"
      environment:
        GOOGLE_APPLICATION_CREDENTIALS: "{{ gcp_service_account_key }}"
      args:
        executable: /bin/bash  

    - name: Check active account
      ansible.builtin.command: /usr/bin/gcloud config get-value account
      register: active_account
      changed_when: false

    - name: Display active account
      debug:
        msg: "{{ active_account.stdout }}"