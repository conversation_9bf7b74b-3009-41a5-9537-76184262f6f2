---
- name: Copy kustomization.yaml to service directory and apply
  hosts: bastion
  tasks:
    - name: Copy kustomization.yaml
      copy:
        src: kustomization.yaml
        dest: /home/<USER>/k3s/kustomization.yaml

    - name: Apply kustomization
      command: kustomize build . | kubectl apply -f -
      args:
        chdir: /home/<USER>/k3s
      become: true  # If you need elevated privileges to run the command
