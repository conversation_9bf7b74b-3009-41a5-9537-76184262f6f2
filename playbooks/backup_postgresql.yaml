---
- name: Backup all PostgreSQL databases and upload to Google Cloud Storage
  hosts: postgres
  vars_files:
    - ../groups_vars/all/vault
  vars:
    backup_directory: "/home/<USER>/tmp"
    bucket_name: "lif-backup-postgresql"
    bucket_folder: "2024"
    gcp_service_account_key: /home/<USER>/gcp_service_account_key.json
    
  tasks:

    - name: Ensure psycopg2 is installed
      ansible.builtin.pip:
        name: psycopg2-binary
        executable: pip3    

    # - name: Run backup script
    #   ansible.builtin.shell: /home/<USER>/backup.sh
    #   args:
    #     executable: /bin/bash
    #   register: script_output

    # - name: Print script output
    #   ansible.builtin.debug:
    #     var: script_output.stdout_lines

    # backup-2024-03-11.tar.gz
    # - name: Compress backup files
    #   ansible.builtin.shell: >
    #     tar -czvf {{ backup_directory }}/backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.tar.gz -C {{ backup_directory }} .
    #   args:
    #     executable: /bin/bash

    # - name: Compress backup files
    #   ansible.builtin.shell: >
    #     tar -czvf {{ backup_directory }}/backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.tar.gz -C {{ backup_directory }} *-backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.dump
    #   args:
    #     executable: /bin/bash    

    - name: Activate service account
      ansible.builtin.command: >
        gcloud auth activate-service-account --key-file={{ gcp_service_account_key }}
      environment:
        GOOGLE_APPLICATION_CREDENTIALS: "{{ gcp_service_account_key }}"

    - name: Check if backup gcs file exists
      ansible.builtin.stat:
        path: "{{ backup_directory }}/backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.tar.gz"
      register: backup_gcs_file

    - name: Upload backups to Google Cloud Storage
      ansible.builtin.shell: >
        gcloud storage cp {{ backup_directory }}/backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.tar.gz gs://{{ bucket_name }}/{{ bucket_folder }}
      args:
        executable: /bin/bash
      environment:
        GOOGLE_APPLICATION_CREDENTIALS: "{{ gcp_service_account_key }}"
      when: backup_gcs_file.stat.exists

    - name: Check if backup file exists
      ansible.builtin.stat:
        path: "{{ backup_directory }}/backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.tar.gz"
      register: backup_file    

    - name: Remove backup file
      ansible.builtin.file:
        path: "{{ backup_directory }}/backup-{{ ansible_date_time.year }}-{{ ansible_date_time.month }}-{{ ansible_date_time.day }}.tar.gz"
        state: absent
      when: backup_file.stat.exists  

    - name: Remove .dump files
      ansible.builtin.shell: >
        rm {{ backup_directory }}/*.dump
      args:
        executable: /bin/bash