---
- name: <PERSON>grate MongoDB Database
  hosts: staging-gcp
  vars:
    mongodb_source_host: "source.mongodb.host"
    mongodb_source_port: 27017
    mongodb_dest_host: "destination.mongodb.host"
    mongodb_dest_port: 27017
    mongodb_database: "your_database"
    mongodb_user: "your_user"
    mongodb_pass: "your_password"
    dump_location: "/tmp/mongodump"

  tasks:
    - name: Ensure the dump directory exists on the source
      ansible.builtin.file:
        path: "{{ dump_location }}"
        state: directory
        mode: '0755'

    - name: Dump MongoDB database from the source server
      community.mongodb.mongodb_shell:
        login_user: "{{ mongodb_user }}"
        login_password: "{{ mongodb_pass }}"
        login_host: "{{ mongodb_source_host }}"
        login_port: "{{ mongodb_source_port }}"
        eval: "mongodump --out={{ dump_location }} --db={{ mongodb_database }}"
      delegate_to: "{{ mongodb_source_host }}"
      run_once: true

    - name: Copy the dump from the source to the destination server
      ansible.builtin.copy:
        src: "{{ dump_location }}"
        dest: "{{ dump_location }}"
        remote_src: true
      delegate_to: "{{ mongodb_dest_host }}"
    
    - name: Restore the dump to the destination MongoDB server
      community.mongodb.mongodb_shell:
        login_user: "{{ mongodb_user }}"
        login_password: "{{ mongodb_pass }}"
        login_host: "{{ mongodb_dest_host }}"
        login_port: "{{ mongodb_dest_port }}"
        eval: "mongorestore {{ dump_location }}/{{ mongodb_database }}"
      delegate_to: "{{ mongodb_dest_host }}"
      run_once: true
