- name: Install RabbitMQ on Ubuntu 22.04
  hosts: rabbitmq
  become: yes
  tasks:
    - name: Install prerequisites
      apt:
        name: 
          - curl
          - gnupg
          - apt-transport-https
        state: latest
        update_cache: yes

    - name: Add repository signing keys
      ansible.builtin.shell: |
        curl -1sLf "https://keys.openpgp.org/vks/v1/by-fingerprint/0A9AF2115F4687BD29803A206B73A36E6026DFCA" | gpg --dearmor | tee /usr/share/keyrings/com.rabbitmq.team.gpg > /dev/null
        curl -1sLf "https://keyserver.ubuntu.com/pks/lookup?op=get&search=0xf77f1eda57ebb1cc" | gpg --dearmor | tee /usr/share/keyrings/net.launchpad.ppa.rabbitmq.erlang.gpg > /dev/null
        curl -1sLf "https://packagecloud.io/rabbitmq/rabbitmq-server/gpgkey" | gpg --dearmor | tee /usr/share/keyrings/io.packagecloud.rabbitmq.gpg > /dev/null

    - name: Add RabbitMQ and Erlang repositories
      ansible.builtin.lineinfile:
        path: /etc/apt/sources.list.d/rabbitmq.list
        create: yes
        line: "{{ item }}"
      loop:
        - "deb [signed-by=/usr/share/keyrings/net.launchpad.ppa.rabbitmq.erlang.gpg] http://ppa.launchpad.net/rabbitmq/rabbitmq-erlang/ubuntu jammy main"
        - "deb-src [signed-by=/usr/share/keyrings/net.launchpad.ppa.rabbitmq.erlang.gpg] http://ppa.launchpad.net/rabbitmq/rabbitmq-erlang/ubuntu jammy main"
        - "deb [signed-by=/usr/share/keyrings/io.packagecloud.rabbitmq.gpg] https://packagecloud.io/rabbitmq/rabbitmq-server/ubuntu/ jammy main"
        - "deb-src [signed-by=/usr/share/keyrings/io.packagecloud.rabbitmq.gpg] https://packagecloud.io/rabbitmq/rabbitmq-server/ubuntu/ jammy main"

    - name: Install Erlang and RabbitMQ
      apt:
        name: 
          - erlang-base
          - erlang-asn1
          - erlang-crypto
          - erlang-eldap
          - erlang-ftp
          - erlang-inets
          - erlang-mnesia
          - erlang-os-mon
          - erlang-parsetools
          - erlang-public-key
          - erlang-runtime-tools
          - erlang-snmp
          - erlang-ssl
          - erlang-syntax-tools
          - erlang-tftp
          - erlang-tools
          - erlang-xmerl
          - rabbitmq-server
        state: latest
        update_cache: yes

    - name: Enable RabbitMQ management plugin
      ansible.builtin.command:
        cmd: rabbitmq-plugins enable rabbitmq_management

    - name: Create RabbitMQ administrative user
      ansible.builtin.command:
        cmd: rabbitmqctl add_user Lif "{{ rabbitmq_password }}"
      register: user_creation_result
      ignore_errors: yes
      failed_when: 
        - user_creation_result.rc != 0
        - "'User \"Lif\" already exists' not in user_creation_result.stderr"  

    - name: Set permissions for user Lif
      ansible.builtin.command:
        cmd: rabbitmqctl set_permissions -p / Lif ".*" ".*" ".*"

    - name: Assign administrator role to user Lif
      ansible.builtin.command:
        cmd: rabbitmqctl set_user_tags Lif administrator

    - name: Delete default guest user
      ansible.builtin.command:
        cmd: rabbitmqctl delete_user guest
