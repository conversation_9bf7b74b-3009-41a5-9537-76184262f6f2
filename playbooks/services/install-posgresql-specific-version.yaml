---
- name: Install PostgreSQL 16.1 on Ubuntu 22.04
  hosts: rabbitmq
  become: yes
  vars_files: ../../groups_vars/all/vault

  tasks:

    - name: Install pip3
      ansible.builtin.apt:
        name: python3-pip
        state: present
        update_cache: yes

    - name: Install psycopg2 Python library
      ansible.builtin.pip:
        name: psycopg2-binary
        state: present
        executable: pip3

    - name: Import PostgreSQL repository signing key
      ansible.builtin.apt_key:
        url: 'https://www.postgresql.org/media/keys/ACCC4CF8.asc'
        state: present

    - name: Add PostgreSQL repository
      ansible.builtin.apt_repository:
        repo: 'deb http://apt.postgresql.org/pub/repos/apt/ jammy-pgdg main'

    - name: Update package list
      ansible.builtin.apt:
        update_cache: yes

    - name: Install PostgreSQL 16.1
      ansible.builtin.apt:
        name: postgresql-16
        state: present
        allow_downgrade: yes
        force: yes
    
    - name: Allow listening on all interfaces
      ansible.builtin.lineinfile:  # FQCN for the lineinfile module
        path: /etc/postgresql/16/main/postgresql.conf
        regexp: '^#?listen_addresses'
        line: "listen_addresses = '*'"
      notify:
        - Restart postgresql  

  handlers:
    - name: Restart postgresql
      ansible.builtin.service:  # FQCN for the service module
        name: postgresql
        state: restarted