- hosts: postgres
  become: true
  tasks:
    - name: Import the repository signing key
      ansible.builtin.apt_key:  # FQCN for the apt_key module
        url: "https://www.postgresql.org/media/keys/ACCC4CF8.asc"
        state: present

    - name: Add PostgreSQL's repository
      ansible.builtin.apt_repository:  # FQCN for the apt_repository module
        repo: "deb http://apt.postgresql.org/pub/repos/apt/ {{ ansible_distribution_release }}-pgdg main"
        state: present

    - name: Install PostgreSQL
      ansible.builtin.apt:  # FQCN for the apt module
        name: postgresql
        state: present  # 'present' ensures the installation of a stable version from the official repo

    - name: Allow listening on all interfaces
      ansible.builtin.lineinfile:  # FQCN for the lineinfile module
        path: /etc/postgresql/16/main/postgresql.conf
        regexp: '^#?listen_addresses'
        line: "listen_addresses = '*'"
      notify:
        - Restart postgresql

  handlers:
    - name: Restart postgresql
      ansible.builtin.service:  # FQCN for the service module
        name: postgresql
        state: restarted
