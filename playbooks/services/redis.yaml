---
- name: Install Redis with <PERSON>rna<PERSON> and Password
  hosts: redis
  become: yes  # Run tasks with elevated privileges
  vars_files:
    - ../../groups_vars/all/vault

  tasks:
    - name: Update apt cache
      ansible.builtin.apt:
        update_cache: yes

    - name: Install required packages
      ansible.builtin.apt:
        name: "{{ item }}"
        state: present
      with_items:
        - build-essential
        - tcl

    - name: Download and extract Redis source
      ansible.builtin.get_url:
        url: "http://download.redis.io/redis-stable.tar.gz"
        dest: "/tmp/redis-stable.tar.gz"
      register: download_result

    - name: Extract Redis source
      ansible.builtin.unarchive:
        src: "/tmp/redis-stable.tar.gz"
        dest: "/tmp"
        remote_src: yes
      when: download_result.changed

    - name: Build and install Redis
      ansible.builtin.shell:
        cmd: "make && make install"
        chdir: "/tmp/redis-stable"
      when: download_result.changed

    - name: Configure Redis
      ansible.builtin.copy:
        src: "../../templates/redis.conf"
        dest: "/etc/redis.conf"
      become: yes

    - name: Update Redis configuration
      ansible.builtin.lineinfile:
        path: "/etc/redis.conf"
        regexp: "{{ item.regexp }}"
        line: "{{ item.line }}"
      loop:
        - { regexp: "^# requirepass foobared", line: "requirepass {{ redis_password }}" }
        - { regexp: "^# rename-command CONFIG \"\"", line: "rename-command CONFIG \"\"" }
      become: yes

    - name: Create systemd service file
      ansible.builtin.copy:
        src: "../../templates/redis.service"
        dest: "/etc/systemd/system/redis.service"
      become: yes

    - name: Reload systemd
      ansible.builtin.systemd:
        daemon_reload: yes  

    - name: Restart Redis service
      ansible.builtin.service:
        name: redis
        state: restarted
        enabled: yes
