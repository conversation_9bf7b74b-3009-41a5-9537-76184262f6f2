# Create repository artifact google
# format: docker
# mode: standar
# location type: region asia-southeast2 jakarta
# encryption Google-managed: encryption key
# Define policies to automatically clean up artifacts: Dry run
---
- name: Create GCP Artifact Registries
  hosts: localhost
  gather_facts: no
  vars:
    gcp_project: "lif-prod"
    location: "asia-southeast2"  # Jakarta
    repositories:
      - name: "anvil"
        description: "Auth service"
      - name: "attendre"
        description: "EXP worker"
      - name: "avis"
        description: "Notification service"
      - name: "bovidae"
        description: "Leaderboard worker"
      - name: "bovidae"
        description: "Leaderboard worker"
      - name: "centaur"
        description: "LIF API Gateway"
      - name: "coceco"
        description: "Challenge service"
      - name: "compositeur"
        description: "Composer worker"
      - name: "core"
        description: "Fitness data service"
      - name: "davinci"
        description: "Storage service"
      - name: "estatico"
        description: "Static webhost"
      - name: "expediteur"
        description: "Sender worker"
      - name: "indiquer"
        description: "Point service"
      - name: "lif-badge"
        description: "Exp service"
      - name: "lifchat"
        description: "chat-service"
      - name: "lifexp"
        description: "Exp service"
      - name: "lifreward"
        description: "LIF reward service"
      - name: "ouvrier"
        description: "Badge worker"
      - name: "paradisiaca"
        description: "Survey service"
      - name: "portal"
        description: "Portal"
      - name: "recevoir"
        description: "Points worker"
      - name: "risala"
        description: "Chat service"
      - name: "stump"
        description: "User service"
      - name: "symvasi"
        description: "Subscription service"
      - name: "diffuserv"
        description: "Content & broadcast"
  tasks:
    - name: Check if Artifact Registry repository exists
      shell: |
        gcloud artifacts repositories describe {{ item.name }} \
        --project={{ gcp_project }} \
        --location={{ location }} \
        --format="value(name)"
      with_items: "{{ repositories }}"
      register: repo_check
      ignore_errors: yes

    - name: Create Artifact Registry repositories
      shell: |
        gcloud artifacts repositories create {{ item.item.name }} \
        --project={{ gcp_project }} \
        --repository-format=docker \
        --location={{ location }} \
        --description="{{ item.item.description }}"
      when: "'not found' in item.stderr"
      with_items: "{{ repo_check.results }}"
      register: creation_results

    - name: Check results of repository creation
      debug:
        var: creation_results

