---
- hosts: mongodb
  become: yes
  tasks:
    - name: Stop MongoDB Service
      shell: "sudo systemctl stop mongod"

    - name: Disable MongoDB Service
      shell: "sudo systemctl disable mongod"

    - name: Remove MongoDB Packages
      shell: "sudo apt-get remove --purge mongodb-org*"

    - name: Remove Configuration Files
      shell: "sudo rm -rf /etc/mongod.conf"

    - name: Remove MongoDB Repository Files
      shell: "sudo rm /etc/apt/sources.list.d/mongodb-org-*.list"

    - name: Remove MongoDB GPG Keyring
      shell: "sudo rm /usr/share/keyrings/mongodb-archive-keyring.gpg"

    - name: Remove MongoDB Data Directory
      shell: "sudo rm -rf /var/lib/mongodb"

    - name: Remove MongoDB Log Directory
      shell: "sudo rm -rf /var/log/mongodb"

    - name: Cleanup Dependencies (Optional)
      shell: "sudo apt-get autoremove"

    - name: Verify MongoDB Service is Disabled
      shell: "sudo systemctl is-enabled mongod"
      register: mongod_service_status
      ignore_errors: true

    - name: Check MongoDB Packages
      shell: "dpkg -l | grep mongodb"

    - name: Check MongoDB Repository Files
      shell: "ls /etc/apt/sources.list.d/mongodb-org-*.list"

    - name: Check MongoDB Data and Log Directories
      shell: "ls /var/lib/mongodb"
      ignore_errors: true

      # Add checks for /var/log/mongodb if needed

    - name: Print Cleanup Status
      debug:
        msg: "MongoDB cleanup completed successfully."

...
