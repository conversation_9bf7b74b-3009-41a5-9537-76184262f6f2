#!/bin/bash

# Configuration
PGUSER="postgres"
BACKUP_DIR="./postgres_backups"
DATE=$(date +%Y-%m-%d_%H-%M-%S)
SESSION_DIR="${BACKUP_DIR}/${DATE}"

# Ensure backup directory exists
mkdir -p "${SESSION_DIR}"

# 1. Backup global roles and permissions
echo "Backing up global roles and permissions..."
pg_dumpall -U $PGUSER -g > "${SESSION_DIR}/_roles_and_globals.sql"

# 2. Get list of user databases (excluding templates and 'postgres')
echo "Fetching list of user databases..."
psql -U $PGUSER -Atc "SELECT datname FROM pg_database WHERE datistemplate = false AND datname NOT IN ('postgres');" > "${SESSION_DIR}/db_list.txt"

# 3. Backup each database in custom format
while read db; do
  echo "Backing up database: ${db}"
  pg_dump -U $PGUSER -Fc -f "${SESSION_DIR}/${db}_full.dump" "${db}"
done < "${SESSION_DIR}/db_list.txt"

# 4. Create a summary
{
  echo "Backup completed at $(date)"
  echo "Databases backed up:"
  cat "${SESSION_DIR}/db_list.txt"
} > "${SESSION_DIR}/_backup_summary.txt"

# 5. Compress the entire session
cd "${BACKUP_DIR}"
tar -czf "${DATE}.tar.gz" "${DATE}"
cd -

echo "✅ Backup and compression completed: ${BACKUP_DIR}/${DATE}.tar.gz"
