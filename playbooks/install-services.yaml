---
- name: Create VMs and install services on GCP using gcloud CLI
  hosts: localhost
  gather_facts: no
  vars:
    gcp_project: "lif-prod"
    zone: "asia-southeast2-a"
    machine_type: "e2-medium"
    image_family: "ubuntu-2004-lts"
    image_project: "ubuntu-os-cloud"
    redis_version: "6.0.5"
    mongodb_version: "4.4.6"
    rabbitmq_version: "3.8.9"
    postgres_version: "13"

  tasks:
    - name: Create VM instance for Redis
      shell: |
        gcloud compute instances create lif-redis-prod \
          --project={{ gcp_project }} \
          --zone={{ zone }} \
          --machine-type={{ machine_type }} \
          --image-family={{ image_family }} \
          --image-project={{ image_project }} \
          --tags=redis-server

    # - name: Create VM instance for MongoDB
    #   shell: |ertjadi
    #     gcloud compute instances create lif-mongodb-prod \
    #       --project={{ gcp_project }} \
    #       --zone={{ zone }} \
    #       --machine-type={{ machine_type }} \
    #       --image-family={{ image_family }} \
    #       --image-project={{ image_project }} \
    #       --tags=mongodb-server

    # - name: Create VM instance for RabbitMQ
    #   shell: |
    #     gcloud compute instances create lif-rabbitmq-prod \
    #       --project={{ gcp_project }} \
    #       --zone={{ zone }} \
    #       --machine-type={{ machine_type }} \
    #       --image-family={{ image_family }} \
    #       --image-project={{ image_project }} \
    #       --tags=rabbitmq-server

    # - name: Create VM instance for PostgreSQL
    #   shell: |
    #     gcloud compute instances create lif-postgres-prod \
    #       --project={{ gcp_project }} \
    #       --zone={{ zone }} \
    #       --machine-type={{ machine_type }} \
    #       --image-family={{ image_family }} \
    #       --image-project={{ image_project }} \
    #       --tags=postgres-server

    # You would need to add tasks to SSH into the VMs and install the specific versions
    # This is a complex task which would typically involve copying installation scripts to the server
    # and executing them, which could look something like this:

    # - name: Install Redis on VM
    #   shell: |
    #     gcloud compute ssh lif-redis-prod --command="sudo apt-get update && sudo apt-get install -y redis-server={{ redis_version }}"
    #   when: redis_version is defined

    # - name: Install MongoDB on VM
    #   shell: |
    #     gcloud compute ssh lif-mongodb-prod --command="sudo apt-get update && sudo apt-get install -y mongodb-org={{ mongodb_version }}"
    #   when: mongodb_version is defined

    # - name: Install RabbitMQ on VM
    #   shell: |
    #     gcloud compute ssh lif-rabbitmq-prod --command="sudo apt-get update && sudo apt-get install -y rabbitmq-server={{ rabbitmq_version }}"
    #   when: rabbitmq_version is defined

    # - name: Install PostgreSQL on VM
    #   shell: |
    #     gcloud compute ssh lif-postgres-prod --command="sudo apt-get update && sudo apt-get install -y postgresql-{{ postgres_version }}"
    #   when: postgres_version is defined

  # Note: The actual installation commands will depend on the operating system of the VM and how the package manager handles versioning.
  # The above commands assume that the required versions are available in the default package repositories.
