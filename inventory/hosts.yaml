#####################
# LIF-STG
######################


[local]
localhost ansible_connection=local

[prod-aws]
apps.lif.id ansible_host=*************
postgres.lif.id ansible_host=**************
mongo.lif.id ansible_host=*************
jenkins.lif.id ansible_host=*************
website.lif.id ansible_host=************

[stg-aws]
docker.lif.stg ansible_host=**************
postgres.lif.stg ansible_host=ansible_host=**************


[vagrant]
postgres.vagrant.db ansible_host=************


[staging-gcp]
#postgres.lif.staging.private ansible_host=***********
postgres.lif.staging.public ansible_host=*************


#####################
# LIF-PROD
######################

[bastion]
bastion_server ansible_host=************** ansible_user=lookman.af ansible_ssh_private_key_file=/home/<USER>/lif.pem

[internal_servers]
lif.prod.redis ansible_host=******** ansible_user=ubuntu
#******** ansible_user=ubuntu
#******** ansible_user=ubuntu
#******** ansible_user=ubuntu

[all:vars]
ansible_ssh_common_args='-o ProxyJump=%h -o StrictHostKeyChecking=no'




#user ubuntu
#ssh -i /Users/<USER>/.ssh/lif ubuntu@*************
#ssh -L local_port:localhost:remote_port user@your_server_ip -p 22
#ssh -i /Users/<USER>/.ssh/lif -L 5433:localhost:5432 ubuntu@*************
#asia-southeast2-a
# ubuntu 20.04 LTS
# operating ubuntu linux
# mongo:3.6.23
# es 8.7.0
# psql (15.2 (Debian 15.2-1.pgdg110+1))
# rabbitmqctl version  3.11.11
# redis_version:7.0.10
# ************** - bastion servers 
# ssh -i  /Users/<USER>/.ssh/lif lookman.af@**************