#####################
# LIF-PROD
######################

#[bastion]
#bastion.server.lif ansible_host=**************

[redis]
********

[rabbitmq]
********

[mongodb]
********

[postgres]
********

; [internal-servers:children]
; redis
; rabbitmq
; mongodb
; postgres

; [internal-servers]
; ******** 
; ******** 
; ********
; ********

#[internal-servers:vars]
#ansible_port = 22
#ansible_user = lookman.af
#private_key_file = ~/.ssh/lif.pem
#running for mac in local 
#ansible_ssh_common_args='-o ProxyCommand="ssh -i /Users/<USER>/.ssh/lif lookman.af@************** -i /home/<USER>/lif.pem -W %h:%p"'

#running for linux
#ansible_ssh_common_args='-o ProxyCommand="ssh -i ~/.ssh/lif.pem lookman.af@************** -i /home/<USER>/lif.pem -W %h:%p"'

# disable for awx tower running from awx server u must disable this 
# 1. ansible_ssh_common_args
# 2.ansible_user = lookman.af
# 3. private_key_file = ~/.ssh/lif
# 4. [internal-servers:vars]  
# 5. [bastion]

