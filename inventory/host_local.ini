#####################
# LIF-PROD
######################

[bastion]
bastion.server.lif ansible_host=**************

[redis]
********

[rabbitmq]
********

[mongodb]
********

[postgres]
********

[internal-servers:children]
redis
rabbitmq
mongodb
postgres

[internal-servers]
******** 
******** 
********
********

[internal-servers:vars]
ansible_port = 22
ansible_user = lookman.af
private_key_file = ~/.ssh/lif
#for mac
#ansible_ssh_common_args='-o ProxyCommand="ssh -i /Users/<USER>/.ssh/lif lookman.af@************** -i /home/<USER>/lif.pem -W %h:%p"'

#for linux
ansible_ssh_common_args='-o ProxyCommand="ssh -i ~/.ssh/lif.pem lookman.af@************** -i /home/<USER>/lif.pem -W %h:%p"'

