


for specfification gke 

- location asia-southeast1-a
- number nodes 3 
- total Vcpus 6 
- total memory 6 GB 

for specification cloudbuild 

name :

anvil
attendre
avis
bovidae
capra
centaur
coceco
compositeur
core
davinci
estatico
expediteur
indiquer
lif-badge
lifchat
lifexp
lifreward
ouvrier
ouvrier-test
paradisiaca
portal
recevoir
risala
stump
symvasi

description :

Auth Service
EXP worker
Notification service
Leaderboard worker
Leaderboard service
LIF API Gateway
Challenge service
Composer worker
Fitness data service
Storage service
Static webhost
Sender worker
Point service
Exp service
chat-service
Exp service
LIF reward service
Badge woker
ouvrier-test
Survey service
Portal
Points worker
Chat service
User service
Subscription service


event : push to branch master 
build : configuration cloudbuild.yaml

repository :
livinginfitness/anvil
livinginfitness/attendre
livinginfitness/avis
livinginfitness/bovidae
livinginfitness/capra
livinginfitness/centaur
livinginfitness/coceco
livinginfitness/compositeur
livinginfitness/lif_core_backend
livinginfitness/davinci
livinginfitness/estatico
livinginfitness/expediteur
livinginfitness/indiquer
livinginfitness/lif-badge-service
livinginfitness/lif-chat
livinginfitness/lif_exp_service
livinginfitness/lif_reward_service
livinginfitness/ouvrier
livinginfitness/ouvrier
livinginfitness/paradisiaca
livinginfitness/lif_chall_frontend
livinginfitness/recevoir
livinginfitness/risala
livinginfitness/stump
livinginfitness/symvasi






for specification artifact registry location asia-southeast2 (Jakarta)

sample url : asia-southeast2-docker.pkg.dev/lif-prod/anvil

anvil
attendre
avis
bovidae
capra
centaur
coceco
compositeur
davinci
estatico
expediteur
indiquer
lif-badge
lif-challenge-portal
lif-chat
lif-core
lif-exp
lif-reward
ouvrier
ouvrier-test
paradisiaca
recevoir
risala
stump
symvasi






Pls create ansible-playbook for deployment gke , cloudbuild , artifact registry

for specfification gke 

- location asia-southeast1-a
- number nodes 3 
- total Vcpus 6 
- total memory 6 GB 

for specification cloudbuild 

name :

anvil
attendre
avis
bovidae
capra
centaur
coceco
compositeur
core
davinci
estatico
expediteur
indiquer
lif-badge
lifchat
lifexp
lifreward
ouvrier
ouvrier-test
paradisiaca
portal
recevoir
risala
stump
symvasi

description :

Auth Service
EXP worker
Notification service
Leaderboard worker
Leaderboard service
LIF API Gateway
Challenge service
Composer worker
Fitness data service
Storage service
Static webhost
Sender worker
Point service
Exp service
chat-service
Exp service
LIF reward service
Badge woker
ouvrier-test
Survey service
Portal
Points worker
Chat service
User service
Subscription service


event : push to branch master 
build : configuration cloudbuild.yaml

repository :
livinginfitness/anvil
livinginfitness/attendre
livinginfitness/avis
livinginfitness/bovidae
livinginfitness/capra
livinginfitness/centaur
livinginfitness/coceco
livinginfitness/compositeur
livinginfitness/lif_core_backend
livinginfitness/davinci
livinginfitness/estatico
livinginfitness/expediteur
livinginfitness/indiquer
livinginfitness/lif-badge-service
livinginfitness/lif-chat
livinginfitness/lif_exp_service
livinginfitness/lif_reward_service
livinginfitness/ouvrier
livinginfitness/ouvrier
livinginfitness/paradisiaca
livinginfitness/lif_chall_frontend
livinginfitness/recevoir
livinginfitness/risala
livinginfitness/stump
livinginfitness/symvasi






for specification artifact registry location asia-southeast2 (Jakarta)

sample url : asia-southeast2-docker.pkg.dev/lif-prod/anvil

anvil
attendre
avis
bovidae
capra
centaur
coceco
compositeur
davinci
estatico
expediteur
indiquer
lif-badge
lif-challenge-portal
lif-chat
lif-core
lif-exp
lif-reward
ouvrier
ouvrier-test
paradisiaca
recevoir
risala
stump
symvasi



Creating an Ansible playbook to deploy the specified Google Cloud services can be complex, so I'll provide you with an outline of how you might structure your playbook and some specific tasks. However, you should consult the official Google Cloud Platform Ansible modules documentation and the specific service documentation to ensure that all details are covered.

Note: This is a high-level guide, and you will likely need to fill in gaps and potentially adjust for the latest GCP Ansible modules if they've changed since my last update.

    Deploy GKE Cluster

For deploying a GKE cluster, you would use the gcp_container_cluster Ansible module.

yaml

---
- name: Deploy GKE Cluster
  hosts: localhost
  gather_facts: no
  tasks:
  - name: Create a GKE cluster
    google.cloud.gcp_container_cluster:
      name: my-gke-cluster
      location: asia-southeast1-a
      initial_node_count: 3
      node_config:
        machine_type: custom-2-2048  # This is an example, adjust for 6 vCPUs and 6GB RAM
      project: your_project_id
      auth_kind: serviceaccount
      service_account_file: /path/to/your/service/account/key.json
      state: present

    Configure Cloud Build

Since Cloud Build triggers are specific to a repository and you have multiple repositories, you'll likely loop over a list of repositories to create them.

yaml

---
- name: Configure Cloud Build Triggers
  hosts: localhost
  gather_facts: no
  tasks:
  - name: Create Cloud Build triggers for each repo
    google.cloud.gcp_cloudbuild_trigger:
      name: "{{ item.name }}"
      description: "{{ item.description }}"
      trigger_template:
        repo_name: "{{ item.repo }}"
        branch_name: "master"
      filename: cloudbuild.yaml
      project: your_project_id
      auth_kind: serviceaccount
      service_account_file: /path/to/your/service/account/key.json
      state: present
    loop:
      - { name: 'anvil', description: 'Auth Service', repo: 'livinginfitness/anvil' }
      # ... Repeat for other repositories

    Setup Artifact Registry

Artifact Registry does not have a direct Ansible module as of my last update. You would likely need to utilize the command module or another way of interfacing with the gcloud CLI to handle these tasks.

yaml

---
- name: Configure Artifact Registry
  hosts: localhost
  gather_facts: no
  tasks:
  - name: Create Artifact Registry Repositories
    command: 
      cmd: "gcloud artifacts repositories create {{ item }} --location=asia-southeast2 --repository-format=docker"
    loop:
      - anvil
      # ... Repeat for other repositories

This is a very high-level outline. For a production setup:

    Always use encrypted secrets, such as Ansible Vault for service account key files.
    Make sure to handle errors and check idempotency. The provided snippets may not be idempotent.
    Thoroughly test the playbooks in a non-production environment before applying them to a production setup.
    Ensure that you have appropriate permissions and API services enabled for these operations.
    Stay updated with the Ansible modules for Google Cloud, as there can be changes or new modules available.

akses kubernetes cluster gke 
/Users/<USER>/.kube/config.gcp





#[prodservers]
# *************
# ************
# *************
# *************
# ************
# *************
# *************

#[prodservers:vars]
#ansible_port = 22
#ansible_user = ec2-user
#private_key_file = /Users/<USER>/.ssh/id_rsa
#ansible_ssh_common_args='-o StrictHostKeyChecking=no -o ProxyCommand="ssh -o \'ForwardAgent yes\' <EMAIL> -p 2222 \'ssh-add /home/<USER>/lif.pem && nc %h %p\'"'



#user ubuntu
#ssh -i /Users/<USER>/.ssh/lif ubuntu@*************
#ssh -L local_port:localhost:remote_port user@your_server_ip -p 22
#ssh -i /Users/<USER>/.ssh/lif -L 5433:localhost:5432 ubuntu@*************
#asia-southeast2-a

# Staging
# ubuntu 20.04 LTS
# operating ubuntu linux
# mongo:3.6.23
# es 8.7.0
# psql (15.2 (Debian 15.2-1.pgdg110+1))
# rabbitmqctl version  3.11.11
# redis_version:7.0.10
# ************** - bastion servers 
# ssh -i  /Users/<USER>/.ssh/lif lookman.af@**************

#mongo -u Lif -p 'Lif2023&10' -h ******** --authenticationDatabase admin
mongorestore -u Lif -p 'Lif2023&10' -h ********  mongo

static ip 

#******** - redis
#******** - rabbitmq
#******** - mongodb
#******** - postgre
 


 db.createUser({
  user: "Lif",
  pwd: "Lif2023&10",
  roles: ["readWrite", "dbAdmin"]
})

###############################


# Staging
ubuntu 20.04 LTS
mongo:3.6.23
psql (15.2 (Debian 15.2-1.pgdg110+1))
rabbitmqctl version  3.11.11
redis_version:7.0.10

# production
OS ubuntu 22.04 LTS
mongo:4.4.25
psql (PostgreSQL) 16.1 (Ubuntu 16.1-1.pgdg22.04+1)
RabbitMQ version: 3.12.10
Redis server v=7.2.3

################################

######
postgres
######

sudo -i -u postgres
psql


postgres@lif-prod-postgres:~$ psql  -f all_databases_backup27nov2023.sql^C
postgres@lif-prod-postgres:~$ pwd
/var/lib/postgresql
postgres@lif-prod-postgres:~$ ls
16  all_databases_backup27nov2023.sql


#####
mongo
#######

mongorestore -u Lif -p 'Lif2023&10' -h ********  mongo



gcloud container clusters get-credentials lif-prod-cluster --zone=asia-southeast2